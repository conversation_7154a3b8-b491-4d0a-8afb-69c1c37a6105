---
title: CompositionAiGuidanceFragment功能与实现规范
type: note
permalink: project-info/composition-ai-guidance-fragment-功能与实现规范
tags:
- project_info
---

# CompositionAiGuidanceFragment 功能与实现规范

## 类职责
`CompositionAiGuidanceFragment` 是用于展示 AI 点拨内容的 Fragment，主要负责加载和显示学生的作文图片以及 AI 生成的点拨建议。

### 主要功能
- **加载学生作文图片**：通过 [compositionViewModel](file:///Users/<USER>/Documents/Android/stu_en_composition/app/src/main/java/com/hailiang/composition/ui/practice/CompositionViewModel.kt#L70-L1074) 获取学生作文图片信息，并使用 Jetpack Compose 动态插入 UI 组件进行展示。
- **显示 AI 点拨结果**：结合 `aiCorrectState` 和 [studentFeedbackState](file:///Users/<USER>/Documents/Android/stu_en_composition/app/src/main/java/com/hljy/composition/ui/practice/CompositionViewModel.kt#L117-L121) 数据状态，动态更新界面中的 AI 判定内容和用户反馈。
- **处理 AI 状态切换**：支持通过 [switchAiTab](file:///Users/<USER>/Documents/Android/stu_en_composition/app/src/main/java/com/hljy/composition/ui/practice/CompositionViewModel.kt#L762-L777) 方法切换不同的 AI 判定维度（如拼写、语法、逻辑等）。
- **用户反馈交互**：允许用户对 AI 的评判结果进行反馈（如点赞或点踩），并通过 `feedbackClick` 回调处理点击事件。
- **ComposeView 生命周期管理**：在 Fragment 中使用 `ComposeView` 时，设置 `setViewCompositionStrategy` 管理生命周期，避免内存泄漏。

### 🛠️ 技术实现说明
- 使用 `ComposeView` 动态插入 Jetpack Compose 构建的 UI 组件，确保界面与数据保持同步。
- 动态更新逻辑基于数据状态的状态驱动 UI 更新，特别是在作文题目识别过程中根据 AI 处理状态动态更新 UI 元素。
- 使用 `collectAsStateWithLifecycle()` 监听 ViewModel 中的状态变化，并自动触发 UI 更新。
- 所有关键字段均遵循 Kotlin 函数参数设计规范，确保必填字段显式传递，非关键字段可选并提供默认值。
- 使用 `when` 表达式替代多个 `if-else` 判断，提升代码可读性。
- 将与状态相关的文案抽离为 Map 映射，便于集中管理和扩展。
- UI 组件命名清晰表达其功能和角色，例如 [CompositionStudentImage](file:///Users/<USER>/Documents/Android/stu_en_composition/app/src/main/java/com/hailiang/composition/ui/widget/CompositionStudentImage.kt#L12-L44) 表示学生作文图片展示组件，命名包含模块名称（如 `Composition`）、用户角色（如 `Student`）和布局位置（如 `Image`），以便于理解其用途。