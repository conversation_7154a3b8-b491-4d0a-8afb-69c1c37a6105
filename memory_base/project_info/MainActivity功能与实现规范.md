---
title: MainActivity功能与实现规范
type: note
permalink: project-info/main-activity-功能与实现规范
tags:
- project_info
---

## 功能概述
`MainActivity` 是整个应用的主入口 Activity，负责初始化核心组件、管理 Fragment 以及协调全局状态。

## 主要功能
- 承载 Fragment 页面（如拍照页、列表页、AI 判定页等），支持切换和嵌套导航。
- 提供统一的 UI 控制策略。
- 绑定 `CompositionViewModel` 并监听 `compositionListLiveData` 数据变化以更新 UI。
- 使用 `FlowEventBus` 实现跨页面通信（如定时刷新事件）。
- 控制空状态视图（vsEmpty）和下拉刷新控件（refreshLayout）的可见性。
- 初始化时间显示逻辑，使用 `LocalDateTime` 更新界面时间与星期信息。
- 管理 Fragment 页面跳转。

## 技术实现说明
- 使用 Glide 加载图片，Lottie 播放动画，ComposeView 加载动态 UI 组件。
- 动态更新逻辑基于数据状态的状态驱动 UI 更新。
- 使用 `collectAsStateWithLifecycle()` 监听数据状态变化。
- 遵循 Kotlin 函数参数设计规范，确保关键字段必填、非关键字段可选。
- 使用 `when` 表达式替代多个 `if-else` 判断，提升代码可读性。

## 与其他模块的关系
- `app`: 主模块，包含所有业务 Fragment。
- `common`: 提供通用工具类（如 ToastUtils）。
- `data`: 提供作文数据模型和 Repository 层。
- `textcorrection`: 可能用于后续文本校正逻辑。