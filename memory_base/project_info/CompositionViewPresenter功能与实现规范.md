---
title: CompositionViewPresenter功能与实现规范
type: note
permalink: project-info/composition-view-presenter-功能与实现规范
tags:
- project_info
---

# CompositionViewPresenter功能与实现规范

## 类职责
`CompositionViewPresenter` 是用于管理作文列表项视图的展示逻辑，负责根据数据状态动态更新 UI 元素，如标题、评分、时间、操作按钮等。

### 主要功能
- **视图创建**：通过 `onCreateViewHolder` 创建并初始化列表项视图。
- **数据绑定**：通过 `onBindViewHolder` 根据 `WorkInfo` 数据对象更新 UI 状态。
- **状态驱动 UI 更新**：根据作文识别状态（成功、失败、进行中）显示不同的 UI 提示。
- **资源释放**：在 `onUnbindViewHolder` 中释放 Lottie 动画资源，防止内存泄漏。

## 技术实现说明
- 使用 `LottieAnimationView` 显示动画效果，支持识别状态变化时的视觉反馈。
- 通过 `Glide` 加载图片，确保高效的图片加载和缓存机制。
- 使用 `SpannableBuilder` 构建带样式文本，增强评分信息的可视化表达。
- 在绑定 ViewHolder 时，依据 `JobStatus` 的不同值设置相应的文字颜色、动画播放或静态图标。
- 使用 `parseJson` 解析作文内容，支持从 JSON 字符串中提取段落信息。
- 在 `onUnbindViewHolder` 中取消 Lottie 动画播放并清空图像资源，确保 RecyclerView 滚动时的资源回收。

## Jetpack Compose 使用规范
- **ComposeView 生命周期管理**：
  - 在 Fragment 中使用 `ComposeView` 时，应调用 `setViewCompositionStrategy` 设置生命周期策略，避免内存泄漏。
  - 推荐使用 `DisposeOnLifecycleDestroyed` 策略以确保 ComposeView 在页面销毁时正确释放资源。
- **状态驱动 UI 更新**：
  - 使用 `collectAsStateWithLifecycle()` 监听 ViewModel 中的状态变化，并自动触发 UI 更新。
  - 避免在 Composable 函数中直接处理业务逻辑，保持 UI 层简洁。
- **UI 组件命名规范**：
  - 命名应清晰体现组件的功能与布局位置，例如 `CompositionStudentLeftWidget` 表示学生作文界面左侧区域的组件。
  - 命名应包含模块名称（如 `Composition`）、用户角色（如 `Student`）和布局位置（如 `Left`），以便于理解其用途。

## Kotlin 函数参数设计规范
- 对于关键字段（如 `schoolworkStateId`）应设为必填参数。
- 非关键字段或具有合理默认行为的参数可以设置为可选参数并提供默认值。
- 即使是必填参数，也可以使用 `String?` 类型以允许显式传递 `null` 值。这种设计可以用于以下场景：
  - 允许显式传递 `null` 以触发特定 UI 状态或区分 "未初始化" 和 "空字符串"。
  - 与可能返回 `null` 的数据源（如 ViewModel）保持一致，避免强制解包引发崩溃。
  - 统一接口风格，保持函数签名一致性。

## 性能优化建议
- **Glide 图片加载优化**：
  - 使用 `override()` 方法指定目标尺寸，减少不必要的图片缩放。
  - 启用 Glide 缓存策略，避免重复下载相同图片。
- **RecyclerView 性能提升**：
  - 使用 DiffUtil 实现高效的 item 差异检测，减少不必要的刷新。
  - 避免在 `onBindViewHolder` 中执行耗时操作，如频繁解析 JSON 数据。
  - 对复杂布局使用预加载或懒加载策略，提高滚动流畅性。
- **Lottie 动画优化**：
  - 对频繁播放的动画使用缓存策略（如 `setAnimation(resId, cacheKey)`）。
  - 控制动画播放范围，避免全屏动画影响性能。
- **内存管理**：
  - 在 `onUnbindViewHolder` 或 `onDetachedFromWindow` 中及时释放资源（如动画、图片）。
  - 避免在适配器中持有 Context 强引用，推荐使用 `holder.itemView.context` 获取上下文。

## 状态驱动逻辑优化实践
- 使用 `when` 表达式替代多个 `if-else` 判断，提升代码可读性。
- 将与状态相关的文案抽离为 Map 映射，便于集中管理和扩展。
- 封装状态处理逻辑为扩展函数，提高代码复用性和解耦业务逻辑。