---
title: 开发环境与依赖
type: note
permalink: project-info/开发环境与依赖
tags:
- project_info
---

# 开发环境与依赖

## 开发环境

- **编程语言**: Kotlin
- **开发工具**: Android Studio
- **构建工具**: Gradle

### 项目构建信息

- 构建命令：`./gradlew assembleDebug`
- 部署方式：通过Android Studio或命令行生成签名APK。

## 依赖库

- **图片加载**: Glide库
- **动画支持**: Lottie库
- **数据处理**: 使用WorkResourceDownload下载资源图片，使用Flow替代LiveData处理数据流。

## 网络与安全

- 启用了网络安全性配置（network_security_config.xml）。

## 模块依赖

- `app`模块为核心功能模块，依赖其他模块（如`common`、`data`、`database`、`textcorrection`）。
- `common`模块提供通用功能（如Toast布局）。
- `data`模块负责数据处理。
- `database`模块管理本地数据库。
- `textcorrection`模块可能负责文本校正逻辑。