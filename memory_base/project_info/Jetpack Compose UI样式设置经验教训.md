---
title: Jetpack Compose UI样式设置经验教训
type: note
permalink: project-info/jetpack-compose-ui-样式设置经验教训
tags:
- project_info
---

# Jetpack Compose UI样式设置经验教训

在Jetpack Compose中设置UI元素的线性渐变背景色、圆角和透明度时，需确保正确导入`Offset`类。常见错误是导入了错误的路径（如`androidx.compose.ui.unit.Offset`或`androidx.compose.ui.graphics.Offset`），正确的路径应为`androidx.compose.ui.geometry.Offset`。

建议统一使用以下方式导入：
```kotlin
import androidx.compose.ui.geometry.Offset
```

并在创建线性渐变时使用如下代码：
```kotlin
.background(
    brush = Brush.linearGradient(
        colors = listOf(Color.White, Color(0xFFEFF8FF)),
        start = Offset(0f, 0f),
        end = Offset(0f, Float.POSITIVE_INFINITY)
    ),
    shape = RoundedCornerShape(15.dp),
    alpha = 0.85f
)
```