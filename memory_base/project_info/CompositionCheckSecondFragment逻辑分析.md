---
title: CompositionCheckSecondFragment逻辑分析
type: note
permalink: project-info/composition-check-second-fragment-逻辑分析
---

# CompositionCheckSecondFragment 逻辑说明

## 类定义与依赖导入
- 继承自 `BaseFragment`，使用 `R.layout.fragment_composition_check_second` 嘉兴布局文件创建视图。

## 成员变量
- `_binding`: 用于绑定布局文件，实现对布局中控件的访问。
- `checkViewModel` 和 `compositionViewModel`: 通过 `activityViewModels` 获取共享的 ViewModel。
- `saList`: 存储句子信息的可变列表。
- `compositionWatcher`: 实现 `CompositionTableLayout.TextWatcher` 接口，监听文本变化并更新句子状态。
- `compositionText`: 存储作文内容的字符串。

## 生命周期方法
- **onCreateView**: 初始化布局并返回根视图。
- **onViewCreated**: 在视图创建后进行初始化操作，包括设置容器的圆角效果、清除原有的子视图并添加新的 ComposeView、启动协程监听 AI 处理结果等。

## 核心功能方法
- **updateGoodBadSentences**: 更新好的和不好的句子信息，从 `aiResponseState` 中获取建议列表并解析出句子信息。
- **findContentRange**: 查找句子在作文内容中的起始和结束位置。

## 销毁视图
- **onDestroyView**: 在视图销毁时释放资源并重置 `_binding`。

## 总结
`CompositionCheckSecondFragment` 主要负责展示作文二次作答的 UI，并根据 AI 分析结果动态更新句子信息。它通过 ViewModel 管理数据流，并利用 Compose 构建现代 UI 组件，实现了高效的交互体验。