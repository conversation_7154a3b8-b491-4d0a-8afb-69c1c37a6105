---
title: MainViewModel功能与实现规范
type: note
permalink: project-info/main-view-model-功能与实现规范
tags:
- project_info
---

# MainViewModel功能与实现规范

## 类职责
`MainViewModel` 是整个应用的核心 ViewModel，负责管理作文列表数据、处理删除操作、新手引导逻辑以及文本校正信息的获取与初始化。

### 主要功能
- **作文列表管理**：通过 `getCompositionList` 方法分页获取作文列表，并更新 `compositionListLiveData`。
- **作文删除**：提供 `deleteComposition` 方法用于删除指定作文，并在删除成功后触发回调。
- **新手引导逻辑**：根据用户是否已阅读过引导内容，决定直接进入拍照页面还是显示引导页面。
- **文本校正信息处理**：获取文本校正模型信息并初始化 `TextCorrectionManager`。

## 技术实现说明
- 使用 `Repository` 层进行数据获取，确保 UI 与数据层解耦。
- 所有网络请求均使用 `viewModelScope.launchCatch` 包裹，统一处理异常情况。
- 数据更新通过 `MutableLiveData` 通知 UI 层，遵循 MVVM 架构设计。
- 使用 `SpManager` .imgur本地状态，如新手引导是否已读、文本校正模型版本等。
- 文本校正模块使用 `TextCorrectionManager` 进行管理，并通过 `initTextCorrectionManager` 初始化模型。

## 使用 Jetpack Compose 的注意事项
- 在 Fragment 中使用 `ComposeView` 动态插入 Jetpack Compose 构建的 UI 组件时，需设置组合生命周期策略以避免内存泄漏。
- 使用 `setViewCompositionStrategy` 管理 `ComposeView` 生命周期，确保资源释放。
- 动态更新逻辑应基于数据状态的状态驱动 UI 更新，特别是在作文题目识别过程中根据 AI 处理状态动态更新 UI 元素。
- 使用 `collectAsStateWithLifecycle()` 监听数据状态变化，确保界面能够根据数据状态自动更新。

## Kotlin 函数参数设计规范
- 对于关键字段（如 `schoolworkStateId`）应设为必填参数。
- 非关键字段或具有合理默认行为的参数可以设置为可选参数并提供默认值。
- 即使是必填参数，也可以使用 `String?` 类型以允许显式传递 `null` 值。这种设计可以用于以下场景：
  - 允许显式传递 `null` 以触发特定 UI 状态或区分 "未初始化" 和 "空字符串"。
  - 与可能返回 `null` 的数据源（如 ViewModel）保持一致，避免强制解包引发崩溃。
  - 统一接口风格，保持函数签名一致性。

## 状态驱动逻辑优化实践
- 使用 `when` 表达式替代多个 `if-else` 判断，提升代码可读性。
- 将与状态相关的文案抽离为 Map 映射，便于集中管理和扩展。
- 封装状态处理逻辑为扩展函数，提高代码复用性和解耦业务逻辑。