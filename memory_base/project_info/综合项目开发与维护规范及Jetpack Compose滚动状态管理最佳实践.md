---
title: 综合项目开发与维护规范及Jetpack Compose滚动状态管理最佳实践
type: note
permalink: project-info/综合项目开发与维护规范及-jetpack-compose-滚动状态管理最佳实践
---

# 综合项目开发与维护规范

## 项目初始化与工具依赖管理
1. 初始化知识库项目并配置正确的存储路径。
2. 激活默认项目以便后续维护。
3. 如果工具调用失败（如`list_memory_projects`或`mcp_memory_list_dir`），需检查记忆系统的工具是否正确安装并可用。
4. 创建新项目前，建议检查并清理冗余项目以避免资源占用冲突。
5. 使用`list_memory_projects`命令列出所有项目，确认是否有重复或不必要的项目。
6. 删除不再需要的项目时，需先切换到其他项目。
7. 确保使用的项目管理工具是最新版本。
8. 安装和使用新工具时，需确保其已正确添加到系统的PATH环境变量中。
9. 当工具无法正常运行时，可以直接安装其依赖组件以避免中间工具带来的复杂性。
10. 确保 `uvx` 命令已正确安装，并检查 Python 版本是否为 3.8 或更高版本。
11. 如果缺少 `uvx` 命令所需环境，需安装 uv。
12. 遇到启动报错 `context deadline exceeded` 时，检查服务参数配置、尝试添加镜像源、确保网络安全限制解除、测试网络连接、增加超时时间、手动安装依赖项。
13. 确认当前工作目录是否正确，避免在错误路径下执行命令。
14. 若项目未注册，应先创建对应项目的知识库。
15. 在执行操作前，确认目标项目已注册并可用。
16. 切换到目标项目后，检查项目根目录的内容以确保操作环境正确。

## 文件导入与管理
1. 确认目标文件夹路径是否正确。
2. 使用`list_dir`工具查看文件夹内容，确保包含需要导入的文件。
3. 针对具体文件使用`read_file`工具读取内容，以便进一步处理。
4. 使用`mcp_basic-memory_write_note`工具将内容写入知识库。
5. 确保导入的文件具有明确的分类和标签，便于后续检索和管理。

## 项目重构规范
1. 将特定功能模块提取为独立方法。
2. 合并到新的功能模块中。
3. 移除不再需要的功能模块。
4. 更新所有引用的地方，确保一致性。
5. 使用自动化工具查找所有引用位置，减少遗漏。

## UI组件样式调整规范
1. 确保图片显示区域横向撑满屏幕。
2. 当一稿有多张图片时，第一张图片横向撑满后，若高度低于卡片缩略图区域，则应拼接后续图片。
3. 替换现有样式时，需确保新样式与整体设计风格一致。

## Logcat错误处理
当遇到Android logcat出现“Unexpected EOF”错误时，可能是由于设备关闭、logd进程崩溃或logcat无法及时读取日志消息等原因导致。以下是解决办法：
1. 增加日志缓冲区大小，通过`adb logcat -G`命令临时设置缓冲区大小，例如`adb logcat -G 5M`。
2. 从Android 5.0开始，可以通过开发者选项调整环形缓冲区的大小。
3. 减少不必要的日志输出，避免日志缓冲区过载。

## 图片上传状态管理
1. 定义明确的状态常量（如默认、加载中、失败、成功）。
2. 在ViewModel中更新状态，并通过LiveData通知UI。
3. 在Fragment或Activity中监听状态变化，并实时更新UI。
4. 提供上传失败后的重试机制，增强用户体验。

## Jetpack Compose滚动状态管理与偏移量配置最佳实践

### LazyColumn滚动状态管理
1. 使用 `rememberLazyListState()` 获取和控制 LazyColumn 滚动状态。
2. 使用 `LaunchedEffect` 监听数据变化并触发自动滚动行为。
3. 在点击事件中调用回调函数以实现手动滚动。
4. 使用 `coroutineScope.launch` 实现非阻塞式滚动动画。
5. 避免频繁调用 `scrollToItem`，加入防抖处理（如记录上一次的状态）。

### 滚动偏移量配置最佳实践
1. **支持自定义偏移量**：在调用 `scrollToItem` 时，新增 `scrollOffset` 参数以支持自定义滚动位置偏移量。
2. **默认值设置**：为 `scrollOffset` 设置默认值为0，确保向后兼容性。
3. **应用场景**：
- 让内容居中显示。
- 避开顶部状态栏或其他固定区域。
- 实现更精准的定位效果。
4. **动态计算逻辑**：可根据实际需求添加动态偏移量计算逻辑，例如根据屏幕高度或可视区域动态调整偏移量。
5. **调用更新**：所有 `scrollToItem` 调用均需更新为带偏移量版本，确保一致性。

### 常见问题及解决方案
1. 初始实现中未正确传递 `scrollOffset` 参数，导致编译错误。
2. 错误地调用了不存在的 `scrollToOffset` 方法，应改为正确传递 `scrollOffset` 参数。
3. 通过逐步修复参数传递错误，最终验证代码无编译错误。

## 规则遵守流程
- 助手需严格遵守用户提供的规则文档 (.lingma/rules/project_rule.md)。
- 每次响应需明确说明遵循的具体规则。
- 需要偏离规则时，必须先征求用户同意。
- 项目扫描阶段优先查看 build.gradle 文件获取编译环境信息。
- 检查关键代码文件 (如 MainActivity.kt、ViewModel 文件) 了解架构模式和技术栈。