package com.hailiang.common.base

import android.app.Activity
import com.hailiang.common.interceptor.CurlLoggerInterceptor
import com.hailiang.core.base.HlBaseApplication
import com.hailiang.hlbugly.HlBuglyManager
import com.hailiang.hlsls.SlsLogManage
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.isDebug
import com.hailiang.libhttp.BaseRetrofit
import com.hailiang.libhttp.request.HlRequestHeader
import com.hailiang.opensdk.OpenSdk
import me.jessyan.autosize.AutoSizeConfig
import me.jessyan.autosize.onAdaptListener
import me.jessyan.autosize.unit.Subunits
import me.jessyan.autosize.utils.ScreenUtils

class XxbApplication : HlBaseApplication() {
    private val tag = javaClass.simpleName
    companion object {
        @JvmStatic
        fun getInstance(): XxbApplication {
            return HlBaseApplication.getInstance() as XxbApplication
        }
    }
    override fun onCreate() {
        super.onCreate()
        HlRequestHeader.setDefaultBuilder(object : HlRequestHeader.DefaultBuilder {
            override fun defaultHeaderMap(): HashMap<String, String>? {
                val map = HashMap<String, String>()
                map["Content-Type"] = "application/json"
                map["Accept"] = "*/*"
                map["otter-token"] = OpenSdk.getLauncherData().getToken()
                map["appType"] = "android"
                return map
            }
        })
        try {
            val buildConfig = Class.forName("${packageName}.BuildConfig")
                .getDeclaredConstructor().newInstance()
            val fields = buildConfig.javaClass.declaredFields
            for (field in fields) {
                field.isAccessible = true
                val name = field.name
                val value = field.get(buildConfig)
                if (name == "BuglyAppID") {
                    HLog.i(tag, "初始化Bugly异常上报")
                    //初始化bugly异常上报
                    HlBuglyManager.getInstance(this)
                        .setAppVersion(HlBuglyManager.getVersionName(this))
                        .init(value as String, isDebug())
                } else if (name == "BUILD_TYPE" && value == "debug") {
                    HLog.i(tag, "添加网络请求Interceptor")
                    BaseRetrofit.putInterceptor(CurlLoggerInterceptor())
                } else if (name == "BASE_URL") {
                    HLog.i(tag, "设置BASE_URL = $value")
                    BaseRetrofit.init(value as String)
                } else if (name == "FLAVOR") {
                    if ("prod" == value) {
                        HLog.i(tag, "初始化正式环境Sls日志")
                        SlsLogManage.getInstance().initPro(this, "hl-advance-teach-log")
                    } else {
                        HLog.i(tag, "初始化测试环境Sls日志")
                        SlsLogManage.getInstance().initSit(this, "hl-advance-teach-log")
                    }
                }
            }
        } catch (_: Exception) {
            HLog.e(tag, "反射BuildConfig失败")
        }

        AutoSizeConfig.getInstance()
            .setCustomFragment(true)
            .setExcludeFontScale(false)
            .setBaseOnWidth(true)
            .setUseDeviceSize(true)
            .setOnAdaptListener(object : onAdaptListener {
                override fun onAdaptBefore(target: Any, activity: Activity) {
                    val screenSize = ScreenUtils.getScreenSize(activity)
                    val screenWidth = screenSize[0]
                    val screenHeight = screenSize[1]
                    AutoSizeConfig.getInstance().screenWidth = screenWidth
                    AutoSizeConfig.getInstance().screenHeight = screenHeight
                    if (screenHeight > screenWidth) {
                        AutoSizeConfig.getInstance().designWidthInDp = 800
                        AutoSizeConfig.getInstance().designHeightInDp = 1280
                        HLog.d(tag, "adapt width=800, height=1280")
                    } else {
                        AutoSizeConfig.getInstance().designWidthInDp = 1280
                        AutoSizeConfig.getInstance().designHeightInDp = 800
                        HLog.d(tag, "adapt width=1280, height=800")
                    }
                }

                override fun onAdaptAfter(target: Any, activity: Activity) {
                }
            })
            .unitsManager
            .setSupportDP(true)
            .setSupportSP(true)
            .supportSubunits = Subunits.MM
    }
}