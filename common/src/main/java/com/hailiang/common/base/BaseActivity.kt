package com.hailiang.common.base

import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.Window
import com.hailiang.core.base.BaseActivity
import com.hailiang.core.theme.setNavigationBarsVisible

open class BaseActivity : BaseActivity() {
    companion object {
        var orientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
    }

    val TAG: String = javaClass.simpleName

    override fun onCreate(savedInstanceState: Bundle?) {
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE)
        requestedOrientation = orientation
        super.onCreate(savedInstanceState)

        initView()
        observeData()
        initData()
    }

    override fun getFullScreenBehavior(): Int {
        return -1
    }

    override fun isFullScreen(): Boolean {
        return true
    }

    protected open fun showNavigationBars(): <PERSON><PERSON>an {
        return true
    }

    override fun refreshImmersive() {
        super.refreshImmersive()
        if (!isFullScreen()) { // 非全屏下，隐藏导航栏
            setNavigationBarsVisible(showNavigationBars())
        }
    }

    open fun initView() {}

    open fun observeData() {}

    open fun initData() {}
}