package com.hailiang.common.ext

import kotlinx.coroutines.*
import kotlin.coroutines.CoroutineContext

// 定义 launchCatch 扩展函数
typealias ErrorBlock = (Throwable) -> Unit
typealias FinallyBlock = () -> Unit

inline fun <T> CoroutineScope.launchCatch(
    noinline errorBlock: ErrorBlock = { _ -> },
    noinline finallyBlock: FinallyBlock = {},
    crossinline block: suspend CoroutineScope.() -> T,
): Job {
    return launch {
        try {
            block()
        } catch (e: Exception) {
            errorBlock(e)
        } finally {
            finallyBlock()
        }
    }
}

// 为 BaseViewModel 添加扩展支持（使用 CoroutineScope 替代）
fun <T> launchCatchOnViewModel(
    viewModelScope: CoroutineScope,
    errorBlock: ErrorBlock = { _ -> },
    finallyBlock: FinallyBlock = {},
    block: suspend CoroutineScope.() -> T,
): Job {
    return viewModelScope.launch {
        try {
            block()
        } catch (e: Exception) {
            errorBlock(e)
        } finally {
            finallyBlock()
        }
    }
}