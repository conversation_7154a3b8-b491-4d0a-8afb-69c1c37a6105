package com.hailiang.common.compose.theme

import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import com.hailiang.xxb.resource.R

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/4/22 14:11
 */
object AppColors {
    val colorPrimary = Color(0xFF1759EE)

    val TextBlack: Color
        @Composable
        get() = colorResource(R.color.black_opaque_85)

    val TextGray = Color(0xFF999999)
    val TextBlue = colorPrimary


    val colorDivider = Color(0xFFF5F5F5)
}