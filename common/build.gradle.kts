plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
}

android {
    namespace = "com.hailiang.xxb.common"
    compileSdk = libs.versions.compileSdk.get().toInt()

    defaultConfig {
        minSdk = libs.versions.minSdk.get().toInt()

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }
    buildFeatures {
        viewBinding = true
        compose = true
    }
    composeOptions {
        kotlinCompilerExtensionVersion = libs.versions.compose.compiler.get()
    }
    configurations.all {
        resolutionStrategy {
            cacheChangingModulesFor(0, TimeUnit.SECONDS) // 禁用缓存
            exclude(group = "com.hailiang.core", module = "libhttp")
            exclude(group = "com.hailiang.hlutil", module = "hlutil")
            exclude(group = "com.hailiang.core", module = "hlcore")
            exclude(group = "com.android.support", module = "support-compat")
        }
    }
}

dependencies {
    api(libs.androidx.core.ktx)
    api(libs.androidx.appcompat)
    api(libs.androidx.recyclerview)
    api(libs.androidx.lifecycle.runtime.ktx)
    api(platform(libs.androidx.compose.bom))
    api(libs.androidx.runtime.compose)
    api(libs.androidx.activity.compose)
    api(libs.androidx.ui)
    api(libs.androidx.ui.graphics)
    api(libs.androidx.ui.tooling.preview)
    api(libs.androidx.material3)
    api(libs.androidx.room)
    api(libs.androidx.room.ktx)
    api(libs.androidx.constraint)
    api(libs.androidx.constraint.compose)

    //hailiang
    api(libs.hailiang.core)
    api(libs.hailiang.util)
    api(libs.hailiang.http)
    api(libs.hailiang.adapter)
    api(libs.hailiang.bugly)
    api(libs.hailiang.autosize)
    api(libs.hailiang.component)
    api(libs.hailiang.resource)
    api(libs.hailiang.opensdk)
    api(libs.hailiang.refresh)
    api(libs.hailiang.sls) {
        exclude(module = "okhttp")
    }
//    api("com.hailiang.data:migration:1.0.2")
//    api("com.hailiang.hlobs:hlobs:1.1.1")
    api("com.hailiang.ui:designsystem:1.0.6")
//    api("com.hailiang.ui:markdown:1.0.4")

    //third
    api(libs.third.okhttp)
    api(libs.third.okhttp.sse)
    api(libs.third.glide)
     api(libs.third.fastjson2)
    api(libs.third.circle.imageview)
    api(libs.third.lottie)
    api(libs.third.blankj)

    //debug
    debugApi(libs.androidx.ui.tooling)
    debugApi(libs.androidx.ui.test.manifest)
    debugApi(libs.third.leakcanary)
    debugImplementation(libs.third.chuck) {
        exclude(module = "constraintlayout")
        exclude(module = "material")
        exclude(module = "kotlin-stdlib")
        exclude(module = "activity-ktx")
        exclude(module = "fragment-ktx")
        exclude(module = "lifecycle-viewmodel-ktx")
        exclude(module = "lifecycle-livedata-ktx")
        exclude(module = "room-ktx")
        exclude(module = "room-runtime")
    }

    //test
    testApi(libs.junit)
    androidTestApi(libs.androidx.junit)
    androidTestApi(libs.androidx.espresso.core)
}