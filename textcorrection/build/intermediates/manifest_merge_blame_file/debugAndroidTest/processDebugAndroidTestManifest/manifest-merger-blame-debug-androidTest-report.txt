1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.hailiang.textcorrection.test" >
4
5    <uses-sdk
5-->/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest711235398109341168.xml:5:5-74
6        android:minSdkVersion="26"
6-->/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest711235398109341168.xml:5:15-41
7        android:targetSdkVersion="26" />
7-->/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest711235398109341168.xml:5:42-71
8
9    <instrumentation
9-->/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest711235398109341168.xml:11:5-15:82
10        android:name="androidx.test.runner.AndroidJUnitRunner"
10-->/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest711235398109341168.xml:11:22-76
11        android:functionalTest="false"
11-->/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest711235398109341168.xml:14:22-52
12        android:handleProfiling="false"
12-->/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest711235398109341168.xml:13:22-53
13        android:label="Tests for com.hailiang.textcorrection.test"
13-->/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest711235398109341168.xml:15:22-80
14        android:targetPackage="com.hailiang.textcorrection.test" />
14-->/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest711235398109341168.xml:12:22-78
15
16    <queries>
16-->[androidx.test:runner:1.5.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/05e0b1b350da6c637dc57547b04063e4/transformed/runner-1.5.2/AndroidManifest.xml:24:5-28:15
17        <package android:name="androidx.test.orchestrator" />
17-->[androidx.test:runner:1.5.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/05e0b1b350da6c637dc57547b04063e4/transformed/runner-1.5.2/AndroidManifest.xml:25:9-62
17-->[androidx.test:runner:1.5.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/05e0b1b350da6c637dc57547b04063e4/transformed/runner-1.5.2/AndroidManifest.xml:25:18-59
18        <package android:name="androidx.test.services" />
18-->[androidx.test:runner:1.5.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/05e0b1b350da6c637dc57547b04063e4/transformed/runner-1.5.2/AndroidManifest.xml:26:9-58
18-->[androidx.test:runner:1.5.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/05e0b1b350da6c637dc57547b04063e4/transformed/runner-1.5.2/AndroidManifest.xml:26:18-55
19        <package android:name="com.google.android.apps.common.testing.services" />
19-->[androidx.test:runner:1.5.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/05e0b1b350da6c637dc57547b04063e4/transformed/runner-1.5.2/AndroidManifest.xml:27:9-83
19-->[androidx.test:runner:1.5.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/05e0b1b350da6c637dc57547b04063e4/transformed/runner-1.5.2/AndroidManifest.xml:27:18-80
20
21        <intent>
21-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:10:9-12:18
22            <action android:name="android.intent.action.MAIN" />
22-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:11:13-65
22-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:11:21-62
23        </intent>
24        <intent>
24-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:13:9-15:18
25            <action android:name="android.intent.action.VIEW" />
25-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:14:13-65
25-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:14:21-62
26        </intent>
27        <intent>
27-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:8:9-12:18
28            <action android:name="android.intent.action.CREATE_DOCUMENT" />
28-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:9:13-76
28-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:9:21-73
29
30            <data android:mimeType="*/*" />
30-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:11:13-44
30-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:11:19-41
31        </intent>
32    </queries>
33
34    <uses-permission android:name="android.permission.INTERNET" />
34-->[:common] /Users/<USER>/Documents/Android/stu_en_composition/common/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-67
34-->[:common] /Users/<USER>/Documents/Android/stu_en_composition/common/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:22-64
35    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
35-->[:common] /Users/<USER>/Documents/Android/stu_en_composition/common/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-81
35-->[:common] /Users/<USER>/Documents/Android/stu_en_composition/common/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:22-78
36    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
36-->[:common] /Users/<USER>/Documents/Android/stu_en_composition/common/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-80
36-->[:common] /Users/<USER>/Documents/Android/stu_en_composition/common/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:22-77
37    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
37-->[com.hailiang.hlsls:hlsls:1.1.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/f0065e3a17cd5489b20b6dfa96052f67/transformed/jetified-hlsls-1.1.3/AndroidManifest.xml:7:5-79
37-->[com.hailiang.hlsls:hlsls:1.1.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/f0065e3a17cd5489b20b6dfa96052f67/transformed/jetified-hlsls-1.1.3/AndroidManifest.xml:7:22-76
38    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
38-->[com.hailiang.hlsls:hlsls:1.1.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/f0065e3a17cd5489b20b6dfa96052f67/transformed/jetified-hlsls-1.1.3/AndroidManifest.xml:8:5-76
38-->[com.hailiang.hlsls:hlsls:1.1.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/f0065e3a17cd5489b20b6dfa96052f67/transformed/jetified-hlsls-1.1.3/AndroidManifest.xml:8:22-73
39    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
39-->[com.hailiang.hlsls:hlsls:1.1.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/f0065e3a17cd5489b20b6dfa96052f67/transformed/jetified-hlsls-1.1.3/AndroidManifest.xml:9:5-76
39-->[com.hailiang.hlsls:hlsls:1.1.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/f0065e3a17cd5489b20b6dfa96052f67/transformed/jetified-hlsls-1.1.3/AndroidManifest.xml:9:22-73
40    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
40-->[com.hailiang.hlsls:hlsls:1.1.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/f0065e3a17cd5489b20b6dfa96052f67/transformed/jetified-hlsls-1.1.3/AndroidManifest.xml:11:5-77
40-->[com.hailiang.hlsls:hlsls:1.1.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/f0065e3a17cd5489b20b6dfa96052f67/transformed/jetified-hlsls-1.1.3/AndroidManifest.xml:11:22-74
41    <uses-permission android:name="android.permission.REORDER_TASKS" />
41-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:24:5-72
41-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:24:22-69
42    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
42-->[com.hailiang.ui:designsystem:1.0.6] /Users/<USER>/.gradle/caches/8.11.1/transforms/f561b510c9d5a3d78fc54c8841a75447/transformed/jetified-designsystem-1.0.6/AndroidManifest.xml:7:5-78
42-->[com.hailiang.ui:designsystem:1.0.6] /Users/<USER>/.gradle/caches/8.11.1/transforms/f561b510c9d5a3d78fc54c8841a75447/transformed/jetified-designsystem-1.0.6/AndroidManifest.xml:7:22-75
43    <uses-permission
43-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:15:5-17:38
44        android:name="android.permission.WAKE_LOCK"
44-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:16:9-52
45        android:maxSdkVersion="25" />
45-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:17:9-35
46    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
46-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:18:5-77
46-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:18:22-74
47
48    <permission
48-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/c6ca12b1c9d1e9bd3d210d5997c3a84d/transformed/core-1.10.1/AndroidManifest.xml:22:5-24:47
49        android:name="com.hailiang.textcorrection.test.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
49-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/c6ca12b1c9d1e9bd3d210d5997c3a84d/transformed/core-1.10.1/AndroidManifest.xml:23:9-81
50        android:protectionLevel="signature" />
50-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/c6ca12b1c9d1e9bd3d210d5997c3a84d/transformed/core-1.10.1/AndroidManifest.xml:24:9-44
51
52    <uses-permission android:name="com.hailiang.textcorrection.test.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
52-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/c6ca12b1c9d1e9bd3d210d5997c3a84d/transformed/core-1.10.1/AndroidManifest.xml:26:5-97
52-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/c6ca12b1c9d1e9bd3d210d5997c3a84d/transformed/core-1.10.1/AndroidManifest.xml:26:22-94
53
54    <application
54-->/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest711235398109341168.xml:7:5-9:19
55        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
55-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/c6ca12b1c9d1e9bd3d210d5997c3a84d/transformed/core-1.10.1/AndroidManifest.xml:28:18-86
56        android:debuggable="true"
57        android:extractNativeLibs="false" >
58        <uses-library android:name="android.test.runner" />
58-->/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest711235398109341168.xml:8:9-60
58-->/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest711235398109341168.xml:8:23-57
59
60        <provider
60-->[com.hailiang.xxb:core:1.1.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/3f3448382a528e2c66510d2e7d994a6b/transformed/jetified-core-1.1.4/AndroidManifest.xml:11:9-19:20
61            android:name="androidx.core.content.FileProvider"
61-->[com.hailiang.xxb:core:1.1.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/3f3448382a528e2c66510d2e7d994a6b/transformed/jetified-core-1.1.4/AndroidManifest.xml:12:13-62
62            android:authorities="com.hailiang.textcorrection.test.fileProvider"
62-->[com.hailiang.xxb:core:1.1.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/3f3448382a528e2c66510d2e7d994a6b/transformed/jetified-core-1.1.4/AndroidManifest.xml:13:13-64
63            android:exported="false"
63-->[com.hailiang.xxb:core:1.1.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/3f3448382a528e2c66510d2e7d994a6b/transformed/jetified-core-1.1.4/AndroidManifest.xml:14:13-37
64            android:grantUriPermissions="true" >
64-->[com.hailiang.xxb:core:1.1.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/3f3448382a528e2c66510d2e7d994a6b/transformed/jetified-core-1.1.4/AndroidManifest.xml:15:13-47
65            <meta-data
65-->[com.hailiang.xxb:core:1.1.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/3f3448382a528e2c66510d2e7d994a6b/transformed/jetified-core-1.1.4/AndroidManifest.xml:16:13-18:54
66                android:name="android.support.FILE_PROVIDER_PATHS"
66-->[com.hailiang.xxb:core:1.1.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/3f3448382a528e2c66510d2e7d994a6b/transformed/jetified-core-1.1.4/AndroidManifest.xml:17:17-67
67                android:resource="@xml/file_paths" />
67-->[com.hailiang.xxb:core:1.1.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/3f3448382a528e2c66510d2e7d994a6b/transformed/jetified-core-1.1.4/AndroidManifest.xml:18:17-51
68        </provider>
69
70        <service android:name="com.hailiang.hlsls.log.LogcatService" />
70-->[com.hailiang.hlsls:hlsls:1.1.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/f0065e3a17cd5489b20b6dfa96052f67/transformed/jetified-hlsls-1.1.3/AndroidManifest.xml:14:9-72
70-->[com.hailiang.hlsls:hlsls:1.1.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/f0065e3a17cd5489b20b6dfa96052f67/transformed/jetified-hlsls-1.1.3/AndroidManifest.xml:14:18-69
71        <service
71-->[com.hailiang.hlsls:hlsls:1.1.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/f0065e3a17cd5489b20b6dfa96052f67/transformed/jetified-hlsls-1.1.3/AndroidManifest.xml:15:9-21:19
72            android:name="com.hailiang.file.FileService"
72-->[com.hailiang.hlsls:hlsls:1.1.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/f0065e3a17cd5489b20b6dfa96052f67/transformed/jetified-hlsls-1.1.3/AndroidManifest.xml:16:13-57
73            android:exported="true" >
73-->[com.hailiang.hlsls:hlsls:1.1.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/f0065e3a17cd5489b20b6dfa96052f67/transformed/jetified-hlsls-1.1.3/AndroidManifest.xml:17:13-36
74            <intent-filter>
74-->[com.hailiang.hlsls:hlsls:1.1.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/f0065e3a17cd5489b20b6dfa96052f67/transformed/jetified-hlsls-1.1.3/AndroidManifest.xml:18:13-20:29
75                <action android:name="com.hailiang.textcorrection.test.aidl.file.FileService" />
75-->[com.hailiang.hlsls:hlsls:1.1.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/f0065e3a17cd5489b20b6dfa96052f67/transformed/jetified-hlsls-1.1.3/AndroidManifest.xml:19:17-81
75-->[com.hailiang.hlsls:hlsls:1.1.3] /Users/<USER>/.gradle/caches/8.11.1/transforms/f0065e3a17cd5489b20b6dfa96052f67/transformed/jetified-hlsls-1.1.3/AndroidManifest.xml:19:25-78
76            </intent-filter>
77        </service>
78
79        <activity
79-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:27:9-34:20
80            android:name="androidx.test.core.app.InstrumentationActivityInvoker$BootstrapActivity"
80-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:28:13-99
81            android:exported="true"
81-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:29:13-36
82            android:theme="@style/WhiteBackgroundTheme" >
82-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:30:13-56
83            <intent-filter android:priority="-100" >
83-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:31:13-33:29
83-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:31:28-51
84                <category android:name="android.intent.category.LAUNCHER" />
84-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:17-77
84-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:27-74
85            </intent-filter>
86        </activity>
87        <activity
87-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:35:9-42:20
88            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity"
88-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:36:13-95
89            android:exported="true"
89-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:37:13-36
90            android:theme="@style/WhiteBackgroundTheme" >
90-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:38:13-56
91            <intent-filter android:priority="-100" >
91-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:31:13-33:29
91-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:31:28-51
92                <category android:name="android.intent.category.LAUNCHER" />
92-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:17-77
92-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:27-74
93            </intent-filter>
94        </activity>
95        <activity
95-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:43:9-50:20
96            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyFloatingActivity"
96-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:44:13-103
97            android:exported="true"
97-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:45:13-36
98            android:theme="@style/WhiteBackgroundDialogTheme" >
98-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:46:13-62
99            <intent-filter android:priority="-100" >
99-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:31:13-33:29
99-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:31:28-51
100                <category android:name="android.intent.category.LAUNCHER" />
100-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:17-77
100-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:27-74
101            </intent-filter>
102        </activity>
103        <activity
103-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:19:9-24:75
104            android:name="com.blankj.utilcode.util.UtilsTransActivity4MainProcess"
104-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:20:13-83
105            android:configChanges="orientation|keyboardHidden|screenSize"
105-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:21:13-74
106            android:exported="false"
106-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:22:13-37
107            android:theme="@style/ActivityTranslucent"
107-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:23:13-55
108            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />
108-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:24:13-72
109        <activity
109-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:25:9-31:75
110            android:name="com.blankj.utilcode.util.UtilsTransActivity"
110-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:26:13-71
111            android:configChanges="orientation|keyboardHidden|screenSize"
111-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:27:13-74
112            android:exported="false"
112-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:28:13-37
113            android:multiprocess="true"
113-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:29:13-40
114            android:theme="@style/ActivityTranslucent"
114-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:30:13-55
115            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />
115-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:31:13-72
116
117        <provider
117-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:33:9-41:20
118            android:name="com.blankj.utilcode.util.UtilsFileProvider"
118-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:34:13-70
119            android:authorities="com.hailiang.textcorrection.test.utilcode.fileprovider"
119-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:35:13-73
120            android:exported="false"
120-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:36:13-37
121            android:grantUriPermissions="true" >
121-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:37:13-47
122            <meta-data
122-->[com.hailiang.xxb:core:1.1.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/3f3448382a528e2c66510d2e7d994a6b/transformed/jetified-core-1.1.4/AndroidManifest.xml:16:13-18:54
123                android:name="android.support.FILE_PROVIDER_PATHS"
123-->[com.hailiang.xxb:core:1.1.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/3f3448382a528e2c66510d2e7d994a6b/transformed/jetified-core-1.1.4/AndroidManifest.xml:17:17-67
124                android:resource="@xml/util_code_provider_paths" />
124-->[com.hailiang.xxb:core:1.1.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/3f3448382a528e2c66510d2e7d994a6b/transformed/jetified-core-1.1.4/AndroidManifest.xml:18:17-51
125        </provider>
126
127        <service
127-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:43:9-49:19
128            android:name="com.blankj.utilcode.util.MessengerUtils$ServerService"
128-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:44:13-81
129            android:exported="false" >
129-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:45:13-37
130            <intent-filter>
130-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:46:13-48:29
131                <action android:name="com.hailiang.textcorrection.test.messenger" />
131-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:47:17-69
131-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:47:25-66
132            </intent-filter>
133        </service>
134
135        <activity
135-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:21:9-26:52
136            android:name="com.chuckerteam.chucker.internal.ui.MainActivity"
136-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:22:13-76
137            android:label="@string/chucker_name"
137-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:23:13-49
138            android:launchMode="singleTask"
138-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:24:13-44
139            android:taskAffinity="com.chuckerteam.chucker.task"
139-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:25:13-64
140            android:theme="@style/Chucker.Theme" />
140-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:26:13-49
141        <activity
141-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:27:9-30:52
142            android:name="com.chuckerteam.chucker.internal.ui.transaction.TransactionActivity"
142-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:28:13-95
143            android:parentActivityName="com.chuckerteam.chucker.internal.ui.MainActivity"
143-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:29:13-90
144            android:theme="@style/Chucker.Theme" />
144-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:30:13-49
145
146        <service
146-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:32:9-35:72
147            android:name="com.chuckerteam.chucker.internal.support.ClearDatabaseService"
147-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:33:13-89
148            android:exported="false"
148-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:34:13-37
149            android:permission="android.permission.BIND_JOB_SERVICE" />
149-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:35:13-69
150
151        <receiver
151-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:37:9-39:40
152            android:name="com.chuckerteam.chucker.internal.support.ClearDatabaseJobIntentServiceReceiver"
152-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:38:13-106
153            android:exported="false" />
153-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:39:13-37
154
155        <provider
155-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:41:9-49:20
156            android:name="com.chuckerteam.chucker.internal.support.ChuckerFileProvider"
156-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:42:13-88
157            android:authorities="com.hailiang.textcorrection.test.com.chuckerteam.chucker.provider"
157-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:43:13-84
158            android:exported="false"
158-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:44:13-37
159            android:grantUriPermissions="true" >
159-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:45:13-47
160            <meta-data
160-->[com.hailiang.xxb:core:1.1.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/3f3448382a528e2c66510d2e7d994a6b/transformed/jetified-core-1.1.4/AndroidManifest.xml:16:13-18:54
161                android:name="android.support.FILE_PROVIDER_PATHS"
161-->[com.hailiang.xxb:core:1.1.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/3f3448382a528e2c66510d2e7d994a6b/transformed/jetified-core-1.1.4/AndroidManifest.xml:17:17-67
162                android:resource="@xml/chucker_provider_paths" />
162-->[com.hailiang.xxb:core:1.1.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/3f3448382a528e2c66510d2e7d994a6b/transformed/jetified-core-1.1.4/AndroidManifest.xml:18:17-51
163        </provider>
164
165        <activity
165-->[androidx.compose.ui:ui-test-manifest:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1135879748bb0bc2152059acb9250c9c/transformed/jetified-ui-test-manifest-1.5.0/AndroidManifest.xml:23:9-25:39
166            android:name="androidx.activity.ComponentActivity"
166-->[androidx.compose.ui:ui-test-manifest:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1135879748bb0bc2152059acb9250c9c/transformed/jetified-ui-test-manifest-1.5.0/AndroidManifest.xml:24:13-63
167            android:exported="true" />
167-->[androidx.compose.ui:ui-test-manifest:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1135879748bb0bc2152059acb9250c9c/transformed/jetified-ui-test-manifest-1.5.0/AndroidManifest.xml:25:13-36
168        <activity
168-->[androidx.compose.ui:ui-tooling-android:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/cd9ad8482eb8ac70c1b7f340f963ad9c/transformed/jetified-ui-tooling-release/AndroidManifest.xml:23:9-25:39
169            android:name="androidx.compose.ui.tooling.PreviewActivity"
169-->[androidx.compose.ui:ui-tooling-android:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/cd9ad8482eb8ac70c1b7f340f963ad9c/transformed/jetified-ui-tooling-release/AndroidManifest.xml:24:13-71
170            android:exported="true" />
170-->[androidx.compose.ui:ui-tooling-android:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/cd9ad8482eb8ac70c1b7f340f963ad9c/transformed/jetified-ui-tooling-release/AndroidManifest.xml:25:13-36
171
172        <provider
172-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0a985f33991c45758575648c015a832b/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:24:9-32:20
173            android:name="androidx.startup.InitializationProvider"
173-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0a985f33991c45758575648c015a832b/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:25:13-67
174            android:authorities="com.hailiang.textcorrection.test.androidx-startup"
174-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0a985f33991c45758575648c015a832b/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:26:13-68
175            android:exported="false" >
175-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0a985f33991c45758575648c015a832b/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:27:13-37
176            <meta-data
176-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0a985f33991c45758575648c015a832b/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:29:13-31:52
177                android:name="androidx.emoji2.text.EmojiCompatInitializer"
177-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0a985f33991c45758575648c015a832b/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:30:17-75
178                android:value="androidx.startup" />
178-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0a985f33991c45758575648c015a832b/transformed/jetified-emoji2-1.4.0/AndroidManifest.xml:31:17-49
179            <meta-data
179-->[androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/68a1a45fd7bb0305a8f21212bb74928f/transformed/jetified-lifecycle-process-2.6.1/AndroidManifest.xml:29:13-31:52
180                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
180-->[androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/68a1a45fd7bb0305a8f21212bb74928f/transformed/jetified-lifecycle-process-2.6.1/AndroidManifest.xml:30:17-78
181                android:value="androidx.startup" />
181-->[androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/68a1a45fd7bb0305a8f21212bb74928f/transformed/jetified-lifecycle-process-2.6.1/AndroidManifest.xml:31:17-49
182            <meta-data
182-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/11b0530ea80610f9cca686b8458a479a/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
183                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
183-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/11b0530ea80610f9cca686b8458a479a/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
184                android:value="androidx.startup" />
184-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/11b0530ea80610f9cca686b8458a479a/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
185        </provider>
186
187        <receiver
187-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/11b0530ea80610f9cca686b8458a479a/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
188            android:name="androidx.profileinstaller.ProfileInstallReceiver"
188-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/11b0530ea80610f9cca686b8458a479a/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
189            android:directBootAware="false"
189-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/11b0530ea80610f9cca686b8458a479a/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
190            android:enabled="true"
190-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/11b0530ea80610f9cca686b8458a479a/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
191            android:exported="true"
191-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/11b0530ea80610f9cca686b8458a479a/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
192            android:permission="android.permission.DUMP" >
192-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/11b0530ea80610f9cca686b8458a479a/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
193            <intent-filter>
193-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/11b0530ea80610f9cca686b8458a479a/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
194                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
194-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/11b0530ea80610f9cca686b8458a479a/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
194-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/11b0530ea80610f9cca686b8458a479a/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
195            </intent-filter>
196            <intent-filter>
196-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/11b0530ea80610f9cca686b8458a479a/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
197                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
197-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/11b0530ea80610f9cca686b8458a479a/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
197-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/11b0530ea80610f9cca686b8458a479a/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
198            </intent-filter>
199            <intent-filter>
199-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/11b0530ea80610f9cca686b8458a479a/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
200                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
200-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/11b0530ea80610f9cca686b8458a479a/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
200-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/11b0530ea80610f9cca686b8458a479a/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
201            </intent-filter>
202            <intent-filter>
202-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/11b0530ea80610f9cca686b8458a479a/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
203                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
203-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/11b0530ea80610f9cca686b8458a479a/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
203-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/11b0530ea80610f9cca686b8458a479a/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
204            </intent-filter>
205        </receiver>
206
207        <service
207-->[androidx.room:room-runtime:2.5.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/bea10cac0e23438c5ef082466347d479/transformed/room-runtime-2.5.1/AndroidManifest.xml:24:9-28:63
208            android:name="androidx.room.MultiInstanceInvalidationService"
208-->[androidx.room:room-runtime:2.5.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/bea10cac0e23438c5ef082466347d479/transformed/room-runtime-2.5.1/AndroidManifest.xml:25:13-74
209            android:directBootAware="true"
209-->[androidx.room:room-runtime:2.5.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/bea10cac0e23438c5ef082466347d479/transformed/room-runtime-2.5.1/AndroidManifest.xml:26:13-43
210            android:exported="false" />
210-->[androidx.room:room-runtime:2.5.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/bea10cac0e23438c5ef082466347d479/transformed/room-runtime-2.5.1/AndroidManifest.xml:27:13-37
211
212        <provider
212-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:32:9-40:20
213            android:name="leakcanary.internal.LeakCanaryFileProvider"
213-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:33:13-70
214            android:authorities="com.squareup.leakcanary.fileprovider.com.hailiang.textcorrection.test"
214-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:34:13-88
215            android:exported="false"
215-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:35:13-37
216            android:grantUriPermissions="true" >
216-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:36:13-47
217            <meta-data
217-->[com.hailiang.xxb:core:1.1.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/3f3448382a528e2c66510d2e7d994a6b/transformed/jetified-core-1.1.4/AndroidManifest.xml:16:13-18:54
218                android:name="android.support.FILE_PROVIDER_PATHS"
218-->[com.hailiang.xxb:core:1.1.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/3f3448382a528e2c66510d2e7d994a6b/transformed/jetified-core-1.1.4/AndroidManifest.xml:17:17-67
219                android:resource="@xml/leak_canary_file_paths" />
219-->[com.hailiang.xxb:core:1.1.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/3f3448382a528e2c66510d2e7d994a6b/transformed/jetified-core-1.1.4/AndroidManifest.xml:18:17-51
220        </provider>
221
222        <activity
222-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:42:9-73:20
223            android:name="leakcanary.internal.activity.LeakActivity"
223-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:43:13-69
224            android:exported="true"
224-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:44:13-36
225            android:icon="@mipmap/leak_canary_icon"
225-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:45:13-52
226            android:label="@string/leak_canary_display_activity_label"
226-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:46:13-71
227            android:taskAffinity="com.squareup.leakcanary.com.hailiang.textcorrection.test"
227-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:47:13-76
228            android:theme="@style/leak_canary_LeakCanary.Base" >
228-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:48:13-63
229            <intent-filter android:label="@string/leak_canary_import_hprof_file" >
229-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:49:13-72:29
229-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:49:28-81
230                <action android:name="android.intent.action.VIEW" />
230-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:14:13-65
230-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:14:21-62
231
232                <category android:name="android.intent.category.DEFAULT" />
232-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:52:17-76
232-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:52:27-73
233                <category android:name="android.intent.category.BROWSABLE" />
233-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:53:17-78
233-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:53:27-75
234
235                <data android:scheme="file" />
235-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:11:13-44
236                <data android:scheme="content" />
236-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:11:13-44
237                <data android:mimeType="*/*" />
237-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:11:13-44
237-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:11:19-41
238                <data android:host="*" />
238-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:11:13-44
239                <data android:pathPattern=".*\\.hprof" />
239-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:11:13-44
240                <data android:pathPattern=".*\\..*\\.hprof" />
240-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:11:13-44
241                <data android:pathPattern=".*\\..*\\..*\\.hprof" />
241-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:11:13-44
242                <data android:pathPattern=".*\\..*\\..*\\..*\\.hprof" />
242-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:11:13-44
243                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.hprof" />
243-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:11:13-44
244                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\.hprof" />
244-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:11:13-44
245                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\..*\\.hprof" />
245-->[com.github.chuckerteam.chucker:library:4.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/AndroidManifest.xml:11:13-44
246                <!--
247            Since hprof isn't a standard MIME type, we have to declare such patterns.
248            Most file providers will generate URIs including their own package name,
249            which contains `.` characters that must be explicitly escaped in pathPattern.
250            @see https://stackoverflow.com/a/31028507/703646
251                -->
252            </intent-filter>
253        </activity>
254
255        <activity-alias
255-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:75:9-92:26
256            android:name="leakcanary.internal.activity.LeakLauncherActivity"
256-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:76:13-77
257            android:banner="@drawable/leak_canary_tv_icon"
257-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:77:13-59
258            android:enabled="@bool/leak_canary_add_launcher_icon"
258-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:78:13-66
259            android:exported="true"
259-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:79:13-36
260            android:icon="@mipmap/leak_canary_icon"
260-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:80:13-52
261            android:label="@string/leak_canary_display_activity_label"
261-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:81:13-71
262            android:targetActivity="leakcanary.internal.activity.LeakActivity"
262-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:82:13-79
263            android:taskAffinity="com.squareup.leakcanary.com.hailiang.textcorrection.test"
263-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:83:13-76
264            android:theme="@style/leak_canary_LeakCanary.Base" >
264-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:84:13-63
265            <intent-filter>
265-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:85:13-91:29
266                <action android:name="android.intent.action.MAIN" />
266-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:11:13-65
266-->[com.blankj:utilcodex:1.31.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/AndroidManifest.xml:11:21-62
267
268                <category android:name="android.intent.category.LAUNCHER" />
268-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:17-77
268-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:27-74
269                <!-- Android TV launcher intent -->
270                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
270-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:90:17-86
270-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:90:27-83
271            </intent-filter>
272        </activity-alias>
273
274        <activity
274-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:94:9-100:68
275            android:name="leakcanary.internal.RequestPermissionActivity"
275-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:95:13-73
276            android:excludeFromRecents="true"
276-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:96:13-46
277            android:icon="@mipmap/leak_canary_icon"
277-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:97:13-52
278            android:label="@string/leak_canary_storage_permission_activity_label"
278-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:98:13-82
279            android:taskAffinity="com.squareup.leakcanary.com.hailiang.textcorrection.test"
279-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:99:13-76
280            android:theme="@style/leak_canary_Theme.Transparent" />
280-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:100:13-65
281
282        <receiver android:name="leakcanary.internal.NotificationReceiver" />
282-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:102:9-77
282-->[com.squareup.leakcanary:leakcanary-android-core:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/AndroidManifest.xml:102:19-74
283
284        <provider
284-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/80ce7b45383e82a877120de67599a293/transformed/jetified-leakcanary-object-watcher-android-2.10/AndroidManifest.xml:8:9-12:40
285            android:name="leakcanary.internal.MainProcessAppWatcherInstaller"
285-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/80ce7b45383e82a877120de67599a293/transformed/jetified-leakcanary-object-watcher-android-2.10/AndroidManifest.xml:9:13-78
286            android:authorities="com.hailiang.textcorrection.test.leakcanary-installer"
286-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/80ce7b45383e82a877120de67599a293/transformed/jetified-leakcanary-object-watcher-android-2.10/AndroidManifest.xml:10:13-72
287            android:enabled="@bool/leak_canary_watcher_auto_install"
287-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/80ce7b45383e82a877120de67599a293/transformed/jetified-leakcanary-object-watcher-android-2.10/AndroidManifest.xml:11:13-69
288            android:exported="false" />
288-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/80ce7b45383e82a877120de67599a293/transformed/jetified-leakcanary-object-watcher-android-2.10/AndroidManifest.xml:12:13-37
289        <provider
289-->[com.squareup.leakcanary:plumber-android:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/280bf180071b406794f6663e23589ddf/transformed/jetified-plumber-android-2.10/AndroidManifest.xml:8:9-12:40
290            android:name="leakcanary.internal.PlumberInstaller"
290-->[com.squareup.leakcanary:plumber-android:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/280bf180071b406794f6663e23589ddf/transformed/jetified-plumber-android-2.10/AndroidManifest.xml:9:13-64
291            android:authorities="com.hailiang.textcorrection.test.plumber-installer"
291-->[com.squareup.leakcanary:plumber-android:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/280bf180071b406794f6663e23589ddf/transformed/jetified-plumber-android-2.10/AndroidManifest.xml:10:13-69
292            android:enabled="@bool/leak_canary_plumber_auto_install"
292-->[com.squareup.leakcanary:plumber-android:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/280bf180071b406794f6663e23589ddf/transformed/jetified-plumber-android-2.10/AndroidManifest.xml:11:13-69
293            android:exported="false" />
293-->[com.squareup.leakcanary:plumber-android:2.10] /Users/<USER>/.gradle/caches/8.11.1/transforms/280bf180071b406794f6663e23589ddf/transformed/jetified-plumber-android-2.10/AndroidManifest.xml:12:13-37
294        <provider
294-->[com.hailiang.ui:autosize:1.0.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/b47a9cd39e9bc342695ab38310882597/transformed/jetified-autosize-1.0.4/AndroidManifest.xml:8:9-12:43
295            android:name="me.jessyan.autosize.InitProvider"
295-->[com.hailiang.ui:autosize:1.0.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/b47a9cd39e9bc342695ab38310882597/transformed/jetified-autosize-1.0.4/AndroidManifest.xml:9:13-60
296            android:authorities="com.hailiang.textcorrection.test.autosize-init-provider"
296-->[com.hailiang.ui:autosize:1.0.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/b47a9cd39e9bc342695ab38310882597/transformed/jetified-autosize-1.0.4/AndroidManifest.xml:10:13-74
297            android:exported="false"
297-->[com.hailiang.ui:autosize:1.0.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/b47a9cd39e9bc342695ab38310882597/transformed/jetified-autosize-1.0.4/AndroidManifest.xml:11:13-37
298            android:multiprocess="true" /> <!-- Just used for obtain context -->
298-->[com.hailiang.ui:autosize:1.0.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/b47a9cd39e9bc342695ab38310882597/transformed/jetified-autosize-1.0.4/AndroidManifest.xml:12:13-40
299        <provider
299-->[io.github.aliyun-sls:aliyun-log-android-sdk:2.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/90d6b43490e94f2454506b0acdd875a1/transformed/jetified-aliyun-log-android-sdk-2.7.0/AndroidManifest.xml:17:9-20:40
300            android:name="com.aliyun.sls.android.producer.provider.SLSContentProvider"
300-->[io.github.aliyun-sls:aliyun-log-android-sdk:2.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/90d6b43490e94f2454506b0acdd875a1/transformed/jetified-aliyun-log-android-sdk-2.7.0/AndroidManifest.xml:18:13-87
301            android:authorities="com.hailiang.textcorrection.test.sls_provider"
301-->[io.github.aliyun-sls:aliyun-log-android-sdk:2.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/90d6b43490e94f2454506b0acdd875a1/transformed/jetified-aliyun-log-android-sdk-2.7.0/AndroidManifest.xml:19:13-64
302            android:exported="false" />
302-->[io.github.aliyun-sls:aliyun-log-android-sdk:2.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/90d6b43490e94f2454506b0acdd875a1/transformed/jetified-aliyun-log-android-sdk-2.7.0/AndroidManifest.xml:20:13-37
303    </application>
304
305</manifest>
