<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.hailiang.ui:designsystem:1.0.6" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/8.11.1/transforms/f561b510c9d5a3d78fc54c8841a75447/transformed/jetified-designsystem-1.0.6/assets"><file name="iconfont/iconfont.ttf" path="/Users/<USER>/.gradle/caches/8.11.1/transforms/f561b510c9d5a3d78fc54c8841a75447/transformed/jetified-designsystem-1.0.6/assets/iconfont/iconfont.ttf"/></source></dataSet><dataSet config=":common" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Android/stu_en_composition/common/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":textcorrection" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config="androidTest" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/src/androidTest/assets"/></dataSet><dataSet config="androidTestDebug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/src/androidTestDebug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/shader_assets/debugAndroidTest/compileDebugAndroidTestShaders/out"/></dataSet></merger>