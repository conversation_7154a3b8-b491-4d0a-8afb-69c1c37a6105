#Fri Jun 20 13:48:18 CST 2025
base.0=/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/dex/debugAndroidTest/mergeExtDexDebugAndroidTest/classes.dex
base.1=/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/dex/debugAndroidTest/mergeLibDexDebugAndroidTest/10/classes.dex
base.10=/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/dex/debugAndroidTest/mergeProjectDexDebugAndroidTest/0/classes.dex
base.11=/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/dex/debugAndroidTest/mergeProjectDexDebugAndroidTest/14/classes.dex
base.12=/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/dex/debugAndroidTest/mergeExtDexDebugAndroidTest/classes2.dex
base.13=/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/dex/debugAndroidTest/mergeExtDexDebugAndroidTest/classes3.dex
base.14=/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/dex/debugAndroidTest/mergeExtDexDebugAndroidTest/classes4.dex
base.2=/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/dex/debugAndroidTest/mergeLibDexDebugAndroidTest/11/classes.dex
base.3=/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/dex/debugAndroidTest/mergeLibDexDebugAndroidTest/12/classes.dex
base.4=/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/dex/debugAndroidTest/mergeLibDexDebugAndroidTest/15/classes.dex
base.5=/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/dex/debugAndroidTest/mergeLibDexDebugAndroidTest/2/classes.dex
base.6=/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/dex/debugAndroidTest/mergeLibDexDebugAndroidTest/5/classes.dex
base.7=/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/dex/debugAndroidTest/mergeLibDexDebugAndroidTest/6/classes.dex
base.8=/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/dex/debugAndroidTest/mergeLibDexDebugAndroidTest/7/classes.dex
base.9=/Users/<USER>/Documents/Android/stu_en_composition/textcorrection/build/intermediates/dex/debugAndroidTest/mergeLibDexDebugAndroidTest/8/classes.dex
path.0=classes.dex
path.1=10/classes.dex
path.10=0/classes.dex
path.11=14/classes.dex
path.12=classes2.dex
path.13=classes3.dex
path.14=classes4.dex
path.2=11/classes.dex
path.3=12/classes.dex
path.4=15/classes.dex
path.5=2/classes.dex
path.6=5/classes.dex
path.7=6/classes.dex
path.8=7/classes.dex
path.9=8/classes.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.10=classes11.dex
renamed.11=classes12.dex
renamed.12=classes13.dex
renamed.13=classes14.dex
renamed.14=classes15.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
renamed.6=classes7.dex
renamed.7=classes8.dex
renamed.8=classes9.dex
renamed.9=classes10.dex
