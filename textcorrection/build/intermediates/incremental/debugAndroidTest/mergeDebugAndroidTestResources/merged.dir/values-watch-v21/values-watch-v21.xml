<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Base.Theme.AppCompat.Dialog" parent="Base.V21.Theme.AppCompat.Dialog">
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowElevation">0dp</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog" parent="Base.V21.Theme.AppCompat.Light.Dialog">
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowElevation">0dp</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dialog" parent="Base.V21.ThemeOverlay.AppCompat.Dialog">
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowElevation">0dp</item>
    </style>
</resources>