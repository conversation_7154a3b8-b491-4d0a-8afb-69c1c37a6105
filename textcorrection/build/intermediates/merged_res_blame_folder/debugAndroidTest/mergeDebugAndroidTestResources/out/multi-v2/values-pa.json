{"logs": [{"outputFile": "com.hailiang.textcorrection.test.textcorrection-mergeDebugAndroidTestResources-78:/values-pa/values-pa.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/94b89705b0416f05ff2804fe35f6c12e/transformed/jetified-material3-1.1.1/res/values-pa/values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,283,395,510,589,686,801,935,1055,1193,1274,1373,1459,1552,1660,1777,1881,2018,2151,2278,2440,2562,2673,2789,2906,2993,3085,3200,3332,3430,3529,3631,3758,3892,3999,4093,4164,4247,4328,4412,4507,4583,4663,4759,4854,4945,5039,5121,5218,5312,5409,5520,5596,5694", "endColumns": "112,114,111,114,78,96,114,133,119,137,80,98,85,92,107,116,103,136,132,126,161,121,110,115,116,86,91,114,131,97,98,101,126,133,106,93,70,82,80,83,94,75,79,95,94,90,93,81,96,93,96,110,75,97,91", "endOffsets": "163,278,390,505,584,681,796,930,1050,1188,1269,1368,1454,1547,1655,1772,1876,2013,2146,2273,2435,2557,2668,2784,2901,2988,3080,3195,3327,3425,3524,3626,3753,3887,3994,4088,4159,4242,4323,4407,4502,4578,4658,4754,4849,4940,5034,5116,5213,5307,5404,5515,5591,5689,5781"}, "to": {"startLines": "33,34,35,36,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,88,91,159,162,164,168,169,170,171,172,173,174,175,176,177,178,179,180,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2975,3088,3203,3315,5067,5146,5243,5358,5492,5612,5750,5831,5930,6016,6109,6217,6334,6438,6575,6708,6835,6997,7119,7230,7346,7463,7550,7642,7757,7889,7987,8086,8188,8315,8449,8556,8841,9064,14370,14603,14788,15147,15223,15303,15399,15494,15585,15679,15761,15858,15952,16049,16160,16236,16334", "endColumns": "112,114,111,114,78,96,114,133,119,137,80,98,85,92,107,116,103,136,132,126,161,121,110,115,116,86,91,114,131,97,98,101,126,133,106,93,70,82,80,83,94,75,79,95,94,90,93,81,96,93,96,110,75,97,91", "endOffsets": "3083,3198,3310,3425,5141,5238,5353,5487,5607,5745,5826,5925,6011,6104,6212,6329,6433,6570,6703,6830,6992,7114,7225,7341,7458,7545,7637,7752,7884,7982,8081,8183,8310,8444,8551,8645,8907,9142,14446,14682,14878,15218,15298,15394,15489,15580,15674,15756,15853,15947,16044,16155,16231,16329,16421"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/b506d2377d150ff1ba498f2cff15408b/transformed/material-1.10.0/res/values-pa/values-pa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,340,419,500,599,688,796,908,991,1055,1147,1216,1275,1360,1423,1485,1543,1607,1668,1722,1836,1894,1954,2008,2078,2205,2286,2365,2500,2576,2653,2782,2866,2948,3003,3058,3124,3193,3270,3356,3435,3503,3579,3649,3714,3816,3911,3984,4078,4171,4245,4314,4408,4464,4547,4614,4698,4786,4848,4912,4975,5042,5139,5245,5336,5438,5497,5556", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,76,78,80,98,88,107,111,82,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,78,134,75,76,128,83,81,54,54,65,68,76,85,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76", "endOffsets": "258,335,414,495,594,683,791,903,986,1050,1142,1211,1270,1355,1418,1480,1538,1602,1663,1717,1831,1889,1949,2003,2073,2200,2281,2360,2495,2571,2648,2777,2861,2943,2998,3053,3119,3188,3265,3351,3430,3498,3574,3644,3709,3811,3906,3979,4073,4166,4240,4309,4403,4459,4542,4609,4693,4781,4843,4907,4970,5037,5134,5240,5331,5433,5492,5551,5628"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,90,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3430,3507,3586,3667,3766,4588,4696,4808,9000,9147,9239,9484,9543,9628,9691,9753,9811,9875,9936,9990,10104,10162,10222,10276,10346,10473,10554,10633,10768,10844,10921,11050,11134,11216,11271,11326,11392,11461,11538,11624,11703,11771,11847,11917,11982,12084,12179,12252,12346,12439,12513,12582,12676,12732,12815,12882,12966,13054,13116,13180,13243,13310,13407,13513,13604,13706,13765,14133", "endLines": "5,37,38,39,40,41,49,50,51,90,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,156", "endColumns": "12,76,78,80,98,88,107,111,82,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,78,134,75,76,128,83,81,54,54,65,68,76,85,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76", "endOffsets": "308,3502,3581,3662,3761,3850,4691,4803,4886,9059,9234,9303,9538,9623,9686,9748,9806,9870,9931,9985,10099,10157,10217,10271,10341,10468,10549,10628,10763,10839,10916,11045,11129,11211,11266,11321,11387,11456,11533,11619,11698,11766,11842,11912,11977,12079,12174,12247,12341,12434,12508,12577,12671,12727,12810,12877,12961,13049,13111,13175,13238,13305,13402,13508,13599,13701,13760,13819,14205"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/4fc144a24dbbc55f2448d27c72f9c72c/transformed/jetified-ui-release/res/values-pa/values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,373,472,560,638,736,824,908,976,1045,1124,1205,1277,1357,1423", "endColumns": "92,82,91,98,87,77,97,87,83,67,68,78,80,71,79,65,117", "endOffsets": "193,276,368,467,555,633,731,819,903,971,1040,1119,1200,1272,1352,1418,1536"}, "to": {"startLines": "52,53,86,87,89,94,95,152,153,154,155,157,158,161,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4891,4984,8650,8742,8912,9308,9386,13824,13912,13996,14064,14210,14289,14531,14883,14963,15029", "endColumns": "92,82,91,98,87,77,97,87,83,67,68,78,80,71,79,65,117", "endOffsets": "4979,5062,8737,8836,8995,9381,9479,13907,13991,14059,14128,14284,14365,14598,14958,15024,15142"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/cd8107e63a7f52f8260c7fe730ff25cc/transformed/appcompat-1.6.1/res/values-pa/values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,416,513,618,704,804,917,995,1072,1163,1256,1350,1444,1544,1637,1732,1826,1917,2008,2087,2197,2300,2396,2507,2609,2719,2878,14451", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "411,508,613,699,799,912,990,1067,1158,1251,1345,1439,1539,1632,1727,1821,1912,2003,2082,2192,2295,2391,2502,2604,2714,2873,2970,14526"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/c6ca12b1c9d1e9bd3d210d5997c3a84d/transformed/core-1.10.1/res/values-pa/values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "42,43,44,45,46,47,48,163", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3855,3953,4055,4158,4259,4361,4459,14687", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "3948,4050,4153,4254,4356,4454,4583,14783"}}]}]}