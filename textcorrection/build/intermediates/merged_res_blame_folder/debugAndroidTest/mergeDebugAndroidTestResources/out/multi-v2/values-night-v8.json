{"logs": [{"outputFile": "com.hailiang.textcorrection.test.textcorrection-mergeDebugAndroidTestResources-78:/values-night-v8/values-night-v8.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/b506d2377d150ff1ba498f2cff15408b/transformed/material-1.10.0/res/values-night-v8/values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1042,1166,1268,1370,1486,1588,1702,1830,1946,2068,2204,2324,2458,2578,2690,2816,2933,3057,3187,3309,3447,3581,3697", "endColumns": "74,110,88,100,106,106,98,106,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1037,1161,1263,1365,1481,1583,1697,1825,1941,2063,2199,2319,2453,2573,2685,2811,2928,3052,3182,3304,3442,3576,3692,3812"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2163,2238,2349,2438,2539,2646,2753,2852,2959,3062,3150,3274,3376,3478,3594,3696,3810,3938,4054,4176,4312,4432,4566,4686,4798,5013,5130,5254,5384,5506,5644,5778,5894", "endColumns": "74,110,88,100,106,106,98,106,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "2233,2344,2433,2534,2641,2748,2847,2954,3057,3145,3269,3371,3473,3589,3691,3805,3933,4049,4171,4307,4427,4561,4681,4793,4919,5125,5249,5379,5501,5639,5773,5889,6009"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/cd8107e63a7f52f8260c7fe730ff25cc/transformed/appcompat-1.6.1/res/values-night-v8/values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "27,28,29,30,31,32,33,59", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1531,1601,1685,1769,1865,1967,2069,4924", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "1596,1680,1764,1860,1962,2064,2158,5008"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/6a4188b3491dfe440ae4dd63d7a84ac1/transformed/jetified-library-4.1.0/res/values-night-v8/values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,178,232,294,351,410,471,530,586,650,708,764,824,888,949,1023,1085,1142,1201,1254,1307,1360,1417,1472", "endColumns": "63,58,53,61,56,58,60,58,55,63,57,55,59,63,60,73,61,56,58,52,52,52,56,54,58", "endOffsets": "114,173,227,289,346,405,466,525,581,645,703,759,819,883,944,1018,1080,1137,1196,1249,1302,1355,1412,1467,1526"}}]}]}