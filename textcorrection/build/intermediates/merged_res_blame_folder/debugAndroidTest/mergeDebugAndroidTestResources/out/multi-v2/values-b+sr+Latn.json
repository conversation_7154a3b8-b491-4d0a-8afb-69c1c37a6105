{"logs": [{"outputFile": "com.hailiang.textcorrection.test.textcorrection-mergeDebugAndroidTestResources-78:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/b506d2377d150ff1ba498f2cff15408b/transformed/material-1.10.0/res/values-b+sr+Latn/values-b+sr+Latn.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,324,401,478,558,666,760,854,986,1067,1133,1226,1294,1357,1460,1520,1586,1642,1713,1773,1827,1939,1996,2057,2111,2187,2312,2399,2482,2621,2703,2786,2917,3005,3083,3137,3193,3259,3333,3411,3500,3582,3658,3734,3809,3881,3988,4078,4151,4243,4339,4411,4487,4583,4636,4718,4785,4872,4959,5021,5085,5148,5217,5322,5432,5528,5636,5694,5754", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,76,76,79,107,93,93,131,80,65,92,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,86,82,138,81,82,130,87,77,53,55,65,73,77,88,81,75,75,74,71,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79", "endOffsets": "319,396,473,553,661,755,849,981,1062,1128,1221,1289,1352,1455,1515,1581,1637,1708,1768,1822,1934,1991,2052,2106,2182,2307,2394,2477,2616,2698,2781,2912,3000,3078,3132,3188,3254,3328,3406,3495,3577,3653,3729,3804,3876,3983,4073,4146,4238,4334,4406,4482,4578,4631,4713,4780,4867,4954,5016,5080,5143,5212,5317,5427,5523,5631,5689,5749,5829"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,91,93,94,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3562,3639,3716,3796,3904,4724,4818,4950,9201,9347,9440,9676,9739,9842,9902,9968,10024,10095,10155,10209,10321,10378,10439,10493,10569,10694,10781,10864,11003,11085,11168,11299,11387,11465,11519,11575,11641,11715,11793,11882,11964,12040,12116,12191,12263,12370,12460,12533,12625,12721,12793,12869,12965,13018,13100,13167,13254,13341,13403,13467,13530,13599,13704,13814,13910,14018,14076,14456", "endLines": "6,38,39,40,41,42,50,51,52,91,93,94,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,157", "endColumns": "12,76,76,79,107,93,93,131,80,65,92,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,86,82,138,81,82,130,87,77,53,55,65,73,77,88,81,75,75,74,71,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79", "endOffsets": "369,3634,3711,3791,3899,3993,4813,4945,5026,9262,9435,9503,9734,9837,9897,9963,10019,10090,10150,10204,10316,10373,10434,10488,10564,10689,10776,10859,10998,11080,11163,11294,11382,11460,11514,11570,11636,11710,11788,11877,11959,12035,12111,12186,12258,12365,12455,12528,12620,12716,12788,12864,12960,13013,13095,13162,13249,13336,13398,13462,13525,13594,13699,13809,13905,14013,14071,14131,14531"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/4fc144a24dbbc55f2448d27c72f9c72c/transformed/jetified-ui-release/res/values-b+sr+Latn/values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,990,1061,1141,1226,1299,1378,1448", "endColumns": "96,86,96,100,85,76,90,91,84,71,70,79,84,72,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,985,1056,1136,1221,1294,1373,1443,1561"}, "to": {"startLines": "53,54,87,88,90,95,96,153,154,155,156,158,159,162,166,167,168", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5031,5128,8844,8941,9115,9508,9585,14136,14228,14313,14385,14536,14616,14873,15234,15313,15383", "endColumns": "96,86,96,100,85,76,90,91,84,71,70,79,84,72,78,69,117", "endOffsets": "5123,5210,8936,9037,9196,9580,9671,14223,14308,14380,14451,14611,14696,14941,15308,15378,15496"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/cd8107e63a7f52f8260c7fe730ff25cc/transformed/appcompat-1.6.1/res/values-b+sr+Latn/values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,2918"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "374,481,582,688,774,878,1000,1085,1167,1258,1351,1446,1540,1640,1733,1828,1933,2024,2115,2201,2306,2412,2515,2622,2731,2838,3008,14786", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "476,577,683,769,873,995,1080,1162,1253,1346,1441,1535,1635,1728,1823,1928,2019,2110,2196,2301,2407,2510,2617,2726,2833,3003,3100,14868"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/c6ca12b1c9d1e9bd3d210d5997c3a84d/transformed/core-1.10.1/res/values-b+sr+Latn/values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "43,44,45,46,47,48,49,164", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3998,4096,4198,4295,4399,4503,4608,15030", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "4091,4193,4290,4394,4498,4603,4719,15126"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/94b89705b0416f05ff2804fe35f6c12e/transformed/jetified-material3-1.1.1/res/values-b+sr+Latn/values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,277,401,512,593,683,791,923,1039,1179,1260,1356,1447,1541,1653,1774,1875,2012,2148,2277,2453,2574,2690,2812,2931,3023,3117,3231,3357,3453,3551,3656,3793,3938,4043,4141,4214,4294,4379,4463,4566,4642,4721,4814,4913,5002,5096,5179,5283,5376,5473,5602,5678,5781", "endColumns": "110,110,123,110,80,89,107,131,115,139,80,95,90,93,111,120,100,136,135,128,175,120,115,121,118,91,93,113,125,95,97,104,136,144,104,97,72,79,84,83,102,75,78,92,98,88,93,82,103,92,96,128,75,102,94", "endOffsets": "161,272,396,507,588,678,786,918,1034,1174,1255,1351,1442,1536,1648,1769,1870,2007,2143,2272,2448,2569,2685,2807,2926,3018,3112,3226,3352,3448,3546,3651,3788,3933,4038,4136,4209,4289,4374,4458,4561,4637,4716,4809,4908,4997,5091,5174,5278,5371,5468,5597,5673,5776,5871"}, "to": {"startLines": "34,35,36,37,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,89,92,160,163,165,169,170,171,172,173,174,175,176,177,178,179,180,181,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3105,3216,3327,3451,5215,5296,5386,5494,5626,5742,5882,5963,6059,6150,6244,6356,6477,6578,6715,6851,6980,7156,7277,7393,7515,7634,7726,7820,7934,8060,8156,8254,8359,8496,8641,8746,9042,9267,14701,14946,15131,15501,15577,15656,15749,15848,15937,16031,16114,16218,16311,16408,16537,16613,16716", "endColumns": "110,110,123,110,80,89,107,131,115,139,80,95,90,93,111,120,100,136,135,128,175,120,115,121,118,91,93,113,125,95,97,104,136,144,104,97,72,79,84,83,102,75,78,92,98,88,93,82,103,92,96,128,75,102,94", "endOffsets": "3211,3322,3446,3557,5291,5381,5489,5621,5737,5877,5958,6054,6145,6239,6351,6472,6573,6710,6846,6975,7151,7272,7388,7510,7629,7721,7815,7929,8055,8151,8249,8354,8491,8636,8741,8839,9110,9342,14781,15025,15229,15572,15651,15744,15843,15932,16026,16109,16213,16306,16403,16532,16608,16711,16806"}}]}]}