{"logs": [{"outputFile": "com.hailiang.textcorrection.test.textcorrection-mergeDebugAndroidTestResources-78:/values-ur/values-ur.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/c6ca12b1c9d1e9bd3d210d5997c3a84d/transformed/core-1.10.1/res/values-ur/values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "42,43,44,45,46,47,48,163", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3915,4013,4115,4217,4321,4424,4522,14927", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "4008,4110,4212,4316,4419,4517,4631,15023"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/4fc144a24dbbc55f2448d27c72f9c72c/transformed/jetified-ui-release/res/values-ur/values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,282,372,469,557,638,731,819,905,972,1039,1122,1207,1282,1357,1423", "endColumns": "93,82,89,96,87,80,92,87,85,66,66,82,84,74,74,65,116", "endOffsets": "194,277,367,464,552,633,726,814,900,967,1034,1117,1202,1277,1352,1418,1535"}, "to": {"startLines": "52,53,86,87,89,94,95,152,153,154,155,157,158,161,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4938,5032,8803,8893,9062,9457,9538,14037,14125,14211,14278,14425,14508,14765,15139,15214,15280", "endColumns": "93,82,89,96,87,80,92,87,85,66,66,82,84,74,74,65,116", "endOffsets": "5027,5110,8888,8985,9145,9533,9626,14120,14206,14273,14340,14503,14588,14835,15209,15275,15392"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/cd8107e63a7f52f8260c7fe730ff25cc/transformed/appcompat-1.6.1/res/values-ur/values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,325,434,520,624,744,821,896,988,1082,1177,1271,1372,1466,1562,1656,1748,1840,1925,2033,2139,2241,2352,2453,2569,2734,2832", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "214,320,429,515,619,739,816,891,983,1077,1172,1266,1367,1461,1557,1651,1743,1835,1920,2028,2134,2236,2347,2448,2564,2729,2827,2913"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,423,529,638,724,828,948,1025,1100,1192,1286,1381,1475,1576,1670,1766,1860,1952,2044,2129,2237,2343,2445,2556,2657,2773,2938,14679", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "418,524,633,719,823,943,1020,1095,1187,1281,1376,1470,1571,1665,1761,1855,1947,2039,2124,2232,2338,2440,2551,2652,2768,2933,3031,14760"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/94b89705b0416f05ff2804fe35f6c12e/transformed/jetified-material3-1.1.1/res/values-ur/values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,287,399,513,591,684,796,927,1046,1181,1262,1362,1454,1550,1663,1789,1894,2034,2173,2301,2495,2619,2734,2854,2989,3082,3173,3293,3413,3510,3611,3713,3853,4000,4102,4201,4273,4352,4438,4525,4636,4722,4803,4902,5004,5097,5196,5277,5380,5475,5573,5709,5795,5893", "endColumns": "113,117,111,113,77,92,111,130,118,134,80,99,91,95,112,125,104,139,138,127,193,123,114,119,134,92,90,119,119,96,100,101,139,146,101,98,71,78,85,86,110,85,80,98,101,92,98,80,102,94,97,135,85,97,89", "endOffsets": "164,282,394,508,586,679,791,922,1041,1176,1257,1357,1449,1545,1658,1784,1889,2029,2168,2296,2490,2614,2729,2849,2984,3077,3168,3288,3408,3505,3606,3708,3848,3995,4097,4196,4268,4347,4433,4520,4631,4717,4798,4897,4999,5092,5191,5272,5375,5470,5568,5704,5790,5888,5978"}, "to": {"startLines": "33,34,35,36,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,88,91,159,162,164,168,169,170,171,172,173,174,175,176,177,178,179,180,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3036,3150,3268,3380,5115,5193,5286,5398,5529,5648,5783,5864,5964,6056,6152,6265,6391,6496,6636,6775,6903,7097,7221,7336,7456,7591,7684,7775,7895,8015,8112,8213,8315,8455,8602,8704,8990,9215,14593,14840,15028,15397,15483,15564,15663,15765,15858,15957,16038,16141,16236,16334,16470,16556,16654", "endColumns": "113,117,111,113,77,92,111,130,118,134,80,99,91,95,112,125,104,139,138,127,193,123,114,119,134,92,90,119,119,96,100,101,139,146,101,98,71,78,85,86,110,85,80,98,101,92,98,80,102,94,97,135,85,97,89", "endOffsets": "3145,3263,3375,3489,5188,5281,5393,5524,5643,5778,5859,5959,6051,6147,6260,6386,6491,6631,6770,6898,7092,7216,7331,7451,7586,7679,7770,7890,8010,8107,8208,8310,8450,8597,8699,8798,9057,9289,14674,14922,15134,15478,15559,15658,15760,15853,15952,16033,16136,16231,16329,16465,16551,16649,16739"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/b506d2377d150ff1ba498f2cff15408b/transformed/material-1.10.0/res/values-ur/values-ur.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,415,493,591,680,780,899,982,1047,1140,1210,1269,1359,1423,1492,1550,1619,1679,1743,1855,1914,1973,2028,2103,2226,2306,2390,2523,2605,2686,2817,2904,2986,3044,3100,3166,3241,3321,3406,3485,3552,3627,3704,3768,3875,3969,4039,4128,4221,4295,4370,4460,4516,4595,4662,4746,4830,4892,4956,5019,5085,5185,5292,5386,5494,5556,5616", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,77,77,97,88,99,118,82,64,92,69,58,89,63,68,57,68,59,63,111,58,58,54,74,122,79,83,132,81,80,130,86,81,57,55,65,74,79,84,78,66,74,76,63,106,93,69,88,92,73,74,89,55,78,66,83,83,61,63,62,65,99,106,93,107,61,59,79", "endOffsets": "254,332,410,488,586,675,775,894,977,1042,1135,1205,1264,1354,1418,1487,1545,1614,1674,1738,1850,1909,1968,2023,2098,2221,2301,2385,2518,2600,2681,2812,2899,2981,3039,3095,3161,3236,3316,3401,3480,3547,3622,3699,3763,3870,3964,4034,4123,4216,4290,4365,4455,4511,4590,4657,4741,4825,4887,4951,5014,5080,5180,5287,5381,5489,5551,5611,5691"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,90,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3494,3572,3650,3728,3826,4636,4736,4855,9150,9294,9387,9631,9690,9780,9844,9913,9971,10040,10100,10164,10276,10335,10394,10449,10524,10647,10727,10811,10944,11026,11107,11238,11325,11407,11465,11521,11587,11662,11742,11827,11906,11973,12048,12125,12189,12296,12390,12460,12549,12642,12716,12791,12881,12937,13016,13083,13167,13251,13313,13377,13440,13506,13606,13713,13807,13915,13977,14345", "endLines": "5,37,38,39,40,41,49,50,51,90,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,156", "endColumns": "12,77,77,77,97,88,99,118,82,64,92,69,58,89,63,68,57,68,59,63,111,58,58,54,74,122,79,83,132,81,80,130,86,81,57,55,65,74,79,84,78,66,74,76,63,106,93,69,88,92,73,74,89,55,78,66,83,83,61,63,62,65,99,106,93,107,61,59,79", "endOffsets": "304,3567,3645,3723,3821,3910,4731,4850,4933,9210,9382,9452,9685,9775,9839,9908,9966,10035,10095,10159,10271,10330,10389,10444,10519,10642,10722,10806,10939,11021,11102,11233,11320,11402,11460,11516,11582,11657,11737,11822,11901,11968,12043,12120,12184,12291,12385,12455,12544,12637,12711,12786,12876,12932,13011,13078,13162,13246,13308,13372,13435,13501,13601,13708,13802,13910,13972,14032,14420"}}]}]}