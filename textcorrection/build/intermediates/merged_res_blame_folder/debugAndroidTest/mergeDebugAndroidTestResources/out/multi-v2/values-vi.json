{"logs": [{"outputFile": "com.hailiang.textcorrection.test.textcorrection-mergeDebugAndroidTestResources-78:/values-vi/values-vi.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/c6ca12b1c9d1e9bd3d210d5997c3a84d/transformed/core-1.10.1/res/values-vi/values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "42,43,44,45,46,47,48,163", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3846,3943,4045,4144,4244,4347,4460,14690", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "3938,4040,4139,4239,4342,4455,4571,14786"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/4fc144a24dbbc55f2448d27c72f9c72c/transformed/jetified-ui-release/res/values-vi/values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,393,493,585,670,763,857,938,1008,1078,1168,1259,1331,1408,1474", "endColumns": "95,85,105,99,91,84,92,93,80,69,69,89,90,71,76,65,113", "endOffsets": "196,282,388,488,580,665,758,852,933,1003,1073,1163,1254,1326,1403,1469,1583"}, "to": {"startLines": "52,53,86,87,89,94,95,152,153,154,155,157,158,161,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4869,4965,8565,8671,8846,9247,9332,13794,13888,13969,14039,14187,14277,14538,14894,14971,15037", "endColumns": "95,85,105,99,91,84,92,93,80,69,69,89,90,71,76,65,113", "endOffsets": "4960,5046,8666,8766,8933,9327,9420,13883,13964,14034,14104,14272,14363,14605,14966,15032,15146"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/94b89705b0416f05ff2804fe35f6c12e/transformed/jetified-material3-1.1.1/res/values-vi/values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,283,386,499,578,669,778,911,1027,1164,1244,1343,1428,1520,1635,1754,1858,1981,2100,2223,2378,2501,2620,2737,2853,2939,3035,3149,3278,3369,3471,3574,3692,3818,3922,4013,4088,4166,4251,4331,4434,4510,4589,4684,4778,4869,4965,5047,5144,5238,5336,5448,5524,5633", "endColumns": "114,112,102,112,78,90,108,132,115,136,79,98,84,91,114,118,103,122,118,122,154,122,118,116,115,85,95,113,128,90,101,102,117,125,103,90,74,77,84,79,102,75,78,94,93,90,95,81,96,93,97,111,75,108,99", "endOffsets": "165,278,381,494,573,664,773,906,1022,1159,1239,1338,1423,1515,1630,1749,1853,1976,2095,2218,2373,2496,2615,2732,2848,2934,3030,3144,3273,3364,3466,3569,3687,3813,3917,4008,4083,4161,4246,4326,4429,4505,4584,4679,4773,4864,4960,5042,5139,5233,5331,5443,5519,5628,5728"}, "to": {"startLines": "33,34,35,36,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,88,91,159,162,164,168,169,170,171,172,173,174,175,176,177,178,179,180,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3008,3123,3236,3339,5051,5130,5221,5330,5463,5579,5716,5796,5895,5980,6072,6187,6306,6410,6533,6652,6775,6930,7053,7172,7289,7405,7491,7587,7701,7830,7921,8023,8126,8244,8370,8474,8771,9001,14368,14610,14791,15151,15227,15306,15401,15495,15586,15682,15764,15861,15955,16053,16165,16241,16350", "endColumns": "114,112,102,112,78,90,108,132,115,136,79,98,84,91,114,118,103,122,118,122,154,122,118,116,115,85,95,113,128,90,101,102,117,125,103,90,74,77,84,79,102,75,78,94,93,90,95,81,96,93,97,111,75,108,99", "endOffsets": "3118,3231,3334,3447,5125,5216,5325,5458,5574,5711,5791,5890,5975,6067,6182,6301,6405,6528,6647,6770,6925,7048,7167,7284,7400,7486,7582,7696,7825,7916,8018,8121,8239,8365,8469,8560,8841,9074,14448,14685,14889,15222,15301,15396,15490,15581,15677,15759,15856,15950,16048,16160,16236,16345,16445"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/b506d2377d150ff1ba498f2cff15408b/transformed/material-1.10.0/res/values-vi/values-vi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,499,579,659,758,872,952,1015,1109,1183,1242,1328,1390,1451,1509,1573,1634,1688,1805,1862,1922,1976,2051,2178,2262,2340,2470,2554,2632,2766,2857,2938,2989,3040,3106,3174,3250,3331,3411,3490,3565,3638,3714,3820,3909,3986,4077,4171,4245,4315,4408,4457,4538,4604,4689,4775,4837,4901,4964,5035,5134,5239,5337,5442,5497,5552", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,78,76,77,79,79,98,113,79,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,77,129,83,77,133,90,80,50,50,65,67,75,80,79,78,74,72,75,105,88,76,90,93,73,69,92,48,80,65,84,85,61,63,62,70,98,104,97,104,54,54,77", "endOffsets": "260,339,416,494,574,654,753,867,947,1010,1104,1178,1237,1323,1385,1446,1504,1568,1629,1683,1800,1857,1917,1971,2046,2173,2257,2335,2465,2549,2627,2761,2852,2933,2984,3035,3101,3169,3245,3326,3406,3485,3560,3633,3709,3815,3904,3981,4072,4166,4240,4310,4403,4452,4533,4599,4684,4770,4832,4896,4959,5030,5129,5234,5332,5437,5492,5547,5625"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,90,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3452,3531,3608,3686,3766,4576,4675,4789,8938,9079,9173,9425,9484,9570,9632,9693,9751,9815,9876,9930,10047,10104,10164,10218,10293,10420,10504,10582,10712,10796,10874,11008,11099,11180,11231,11282,11348,11416,11492,11573,11653,11732,11807,11880,11956,12062,12151,12228,12319,12413,12487,12557,12650,12699,12780,12846,12931,13017,13079,13143,13206,13277,13376,13481,13579,13684,13739,14109", "endLines": "5,37,38,39,40,41,49,50,51,90,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,156", "endColumns": "12,78,76,77,79,79,98,113,79,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,77,129,83,77,133,90,80,50,50,65,67,75,80,79,78,74,72,75,105,88,76,90,93,73,69,92,48,80,65,84,85,61,63,62,70,98,104,97,104,54,54,77", "endOffsets": "310,3526,3603,3681,3761,3841,4670,4784,4864,8996,9168,9242,9479,9565,9627,9688,9746,9810,9871,9925,10042,10099,10159,10213,10288,10415,10499,10577,10707,10791,10869,11003,11094,11175,11226,11277,11343,11411,11487,11568,11648,11727,11802,11875,11951,12057,12146,12223,12314,12408,12482,12552,12645,12694,12775,12841,12926,13012,13074,13138,13201,13272,13371,13476,13574,13679,13734,13789,14182"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/cd8107e63a7f52f8260c7fe730ff25cc/transformed/appcompat-1.6.1/res/values-vi/values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,422,524,633,717,820,939,1017,1093,1184,1277,1372,1466,1566,1659,1754,1848,1939,2030,2114,2218,2326,2427,2532,2647,2752,2909,14453", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "417,519,628,712,815,934,1012,1088,1179,1272,1367,1461,1561,1654,1749,1843,1934,2025,2109,2213,2321,2422,2527,2642,2747,2904,3003,14533"}}]}]}