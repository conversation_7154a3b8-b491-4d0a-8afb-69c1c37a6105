{"logs": [{"outputFile": "com.hailiang.textcorrection.test.textcorrection-mergeDebugAndroidTestResources-78:/values-ru/values-ru.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/c6ca12b1c9d1e9bd3d210d5997c3a84d/transformed/core-1.10.1/res/values-ru/values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "44,45,46,47,48,49,50,165", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4031,4129,4231,4332,4433,4538,4641,15060", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "4124,4226,4327,4428,4533,4636,4753,15156"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/4fc144a24dbbc55f2448d27c72f9c72c/transformed/jetified-ui-release/res/values-ru/values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,379,481,573,655,745,833,915,986,1056,1140,1227,1299,1383,1453", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "193,276,374,476,568,650,740,828,910,981,1051,1135,1222,1294,1378,1448,1571"}, "to": {"startLines": "54,55,88,89,91,96,97,154,155,156,157,159,160,163,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5084,5177,8843,8941,9124,9536,9618,14171,14259,14341,14412,14562,14646,14905,15265,15349,15419", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "5172,5255,8936,9038,9211,9613,9703,14254,14336,14407,14477,14641,14728,14972,15344,15414,15537"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/b506d2377d150ff1ba498f2cff15408b/transformed/material-1.10.0/res/values-ru/values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,719,810,907,1044,1136,1202,1301,1378,1441,1559,1620,1685,1742,1812,1873,1927,2043,2100,2162,2216,2290,2418,2506,2592,2729,2813,2898,3032,3123,3199,3253,3304,3370,3442,3520,3616,3698,3778,3854,3931,4008,4115,4204,4277,4367,4462,4536,4617,4710,4765,4846,4912,4998,5083,5145,5209,5272,5344,5442,5541,5636,5728,5786,5841", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,77,77,83,97,90,96,136,91,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,85,136,83,84,133,90,75,53,50,65,71,77,95,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79", "endOffsets": "376,454,532,616,714,805,902,1039,1131,1197,1296,1373,1436,1554,1615,1680,1737,1807,1868,1922,2038,2095,2157,2211,2285,2413,2501,2587,2724,2808,2893,3027,3118,3194,3248,3299,3365,3437,3515,3611,3693,3773,3849,3926,4003,4110,4199,4272,4362,4457,4531,4612,4705,4760,4841,4907,4993,5078,5140,5204,5267,5339,5437,5536,5631,5723,5781,5836,5916"}, "to": {"startLines": "2,39,40,41,42,43,51,52,53,92,94,95,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3602,3680,3758,3842,3940,4758,4855,4992,9216,9360,9459,9708,9771,9889,9950,10015,10072,10142,10203,10257,10373,10430,10492,10546,10620,10748,10836,10922,11059,11143,11228,11362,11453,11529,11583,11634,11700,11772,11850,11946,12028,12108,12184,12261,12338,12445,12534,12607,12697,12792,12866,12947,13040,13095,13176,13242,13328,13413,13475,13539,13602,13674,13772,13871,13966,14058,14116,14482", "endLines": "7,39,40,41,42,43,51,52,53,92,94,95,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,158", "endColumns": "12,77,77,83,97,90,96,136,91,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,85,136,83,84,133,90,75,53,50,65,71,77,95,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79", "endOffsets": "426,3675,3753,3837,3935,4026,4850,4987,5079,9277,9454,9531,9766,9884,9945,10010,10067,10137,10198,10252,10368,10425,10487,10541,10615,10743,10831,10917,11054,11138,11223,11357,11448,11524,11578,11629,11695,11767,11845,11941,12023,12103,12179,12256,12333,12440,12529,12602,12692,12787,12861,12942,13035,13090,13171,13237,13323,13408,13470,13534,13597,13669,13767,13866,13961,14053,14111,14166,14557"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/94b89705b0416f05ff2804fe35f6c12e/transformed/jetified-material3-1.1.1/res/values-ru/values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,395,509,586,679,790,927,1040,1176,1256,1350,1439,1533,1644,1763,1869,1994,2118,2240,2416,2536,2655,2779,2896,2983,3079,3196,3325,3419,3529,3633,3760,3894,3997,4092,4173,4251,4341,4424,4528,4604,4684,4778,4875,4965,5056,5140,5242,5336,5430,5573,5649,5751", "endColumns": "113,111,113,113,76,92,110,136,112,135,79,93,88,93,110,118,105,124,123,121,175,119,118,123,116,86,95,116,128,93,109,103,126,133,102,94,80,77,89,82,103,75,79,93,96,89,90,83,101,93,93,142,75,101,92", "endOffsets": "164,276,390,504,581,674,785,922,1035,1171,1251,1345,1434,1528,1639,1758,1864,1989,2113,2235,2411,2531,2650,2774,2891,2978,3074,3191,3320,3414,3524,3628,3755,3889,3992,4087,4168,4246,4336,4419,4523,4599,4679,4773,4870,4960,5051,5135,5237,5331,5425,5568,5644,5746,5839"}, "to": {"startLines": "35,36,37,38,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,90,93,161,164,166,170,171,172,173,174,175,176,177,178,179,180,181,182,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3148,3262,3374,3488,5260,5337,5430,5541,5678,5791,5927,6007,6101,6190,6284,6395,6514,6620,6745,6869,6991,7167,7287,7406,7530,7647,7734,7830,7947,8076,8170,8280,8384,8511,8645,8748,9043,9282,14733,14977,15161,15542,15618,15698,15792,15889,15979,16070,16154,16256,16350,16444,16587,16663,16765", "endColumns": "113,111,113,113,76,92,110,136,112,135,79,93,88,93,110,118,105,124,123,121,175,119,118,123,116,86,95,116,128,93,109,103,126,133,102,94,80,77,89,82,103,75,79,93,96,89,90,83,101,93,93,142,75,101,92", "endOffsets": "3257,3369,3483,3597,5332,5425,5536,5673,5786,5922,6002,6096,6185,6279,6390,6509,6615,6740,6864,6986,7162,7282,7401,7525,7642,7729,7825,7942,8071,8165,8275,8379,8506,8640,8743,8838,9119,9355,14818,15055,15260,15613,15693,15787,15884,15974,16065,16149,16251,16345,16439,16582,16658,16760,16853"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/cd8107e63a7f52f8260c7fe730ff25cc/transformed/appcompat-1.6.1/res/values-ru/values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "431,546,648,747,833,938,1059,1138,1214,1306,1400,1495,1588,1683,1777,1873,1968,2060,2152,2241,2347,2454,2552,2661,2768,2882,3048,14823", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "541,643,742,828,933,1054,1133,1209,1301,1395,1490,1583,1678,1772,1868,1963,2055,2147,2236,2342,2449,2547,2656,2763,2877,3043,3143,14900"}}]}]}