{"logs": [{"outputFile": "com.hailiang.textcorrection.test.textcorrection-mergeDebugAndroidTestResources-78:/values-v21/values-v21.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/cd8107e63a7f52f8260c7fe730ff25cc/transformed/appcompat-1.6.1/res/values-v21/values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8283,8468,11238,11435,11634,11757,11880,11993,12176,12431,12632,12721,12832,13065,13166,13261,13384,13513,13630,13807,13906,14041,14184,14319,14438,14639,14758,14851,14962,15018,15125,15320,15431,15564,15659,15750,15841,15934,16051,16190,16261,16344,16967,17024,17082,17706", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8278,8463,11233,11430,11629,11752,11875,11988,12171,12426,12627,12716,12827,13060,13161,13256,13379,13508,13625,13802,13901,14036,14179,14314,14433,14634,14753,14846,14957,15013,15120,15315,15426,15559,15654,15745,15836,15929,16046,16185,16256,16339,16962,17019,17077,17701,18337"}, "to": {"startLines": "37,38,39,40,41,42,43,44,45,46,47,48,49,50,52,54,55,56,57,59,61,62,63,64,65,67,69,71,73,75,77,78,83,85,87,88,89,91,93,94,95,96,101,113,156,159,202,217,229,231,233,235,238,242,245,246,247,250,251,252,253,254,255,258,259,261,263,265,267,271,273,274,275,276,278,282,284,286,287,288,289,290,291,322,323,324,334,335,336,348", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2794,2885,2988,3091,3196,3303,3412,3521,3630,3739,3848,3955,4058,4177,4332,4487,4592,4713,4814,4961,5102,5205,5324,5431,5534,5689,5860,6009,6174,6331,6482,6601,6952,7101,7250,7362,7509,7662,7809,7884,7973,8060,8585,9677,12435,12620,15390,16523,17375,17498,17621,17734,17917,18172,18373,18462,18573,18806,18907,19002,19125,19254,19371,19548,19647,19782,19925,20060,20179,20380,20499,20592,20703,20759,20866,21061,21172,21305,21400,21491,21582,21675,21792,24225,24296,24379,25002,25059,25117,25741", "endLines": "37,38,39,40,41,42,43,44,45,46,47,48,49,51,53,54,55,56,58,60,61,62,63,64,66,68,70,72,74,76,77,82,84,86,87,88,90,92,93,94,95,96,101,155,158,201,204,219,230,232,234,237,241,244,245,246,249,250,251,252,253,254,257,258,260,262,264,266,270,272,273,274,275,277,281,283,285,286,287,288,289,290,292,322,323,333,334,335,347,359", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "2880,2983,3086,3191,3298,3407,3516,3625,3734,3843,3950,4053,4172,4327,4482,4587,4708,4809,4956,5097,5200,5319,5426,5529,5684,5855,6004,6169,6326,6477,6596,6947,7096,7245,7357,7504,7657,7804,7879,7968,8055,8156,8683,12430,12615,15385,15582,16717,17493,17616,17729,17912,18167,18368,18457,18568,18801,18902,18997,19120,19249,19366,19543,19642,19777,19920,20055,20174,20375,20494,20587,20698,20754,20861,21056,21167,21300,21395,21486,21577,21670,21787,21926,24291,24374,24997,25054,25112,25736,26372"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/4e71911f599680b941b147d6eed99344/transformed/jetified-utilcodex-1.31.1/res/values-v21/values-v21.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "14", "endColumns": "12", "endOffsets": "869"}, "to": {"startLines": "24", "startColumns": "4", "startOffsets": "1975", "endLines": "36", "endColumns": "12", "endOffsets": "2789"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/8686e3d8ad1bc2c2e8dbdf5c19e0b138/transformed/jetified-leakcanary-android-core-2.10/res/values-v21/values-v21.xml", "from": {"startLines": "2,6", "startColumns": "4,4", "startOffsets": "55,313", "endLines": "5,9", "endColumns": "10,10", "endOffsets": "308,535"}, "to": {"startLines": "594,598", "startColumns": "4,4", "startOffsets": "40663,40921", "endLines": "597,601", "endColumns": "10,10", "endOffsets": "40916,41143"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/7559635a77f31cda4aa3f48f6353f2cc/transformed/jetified-core-1.5.0/res/values-v21/values.xml", "from": {"startLines": "4,13", "startColumns": "0,0", "startOffsets": "180,655", "endLines": "12,21", "endColumns": "8,8", "endOffsets": "654,1127"}, "to": {"startLines": "550,559", "startColumns": "4,4", "startOffsets": "38036,38515", "endLines": "558,567", "endColumns": "8,8", "endOffsets": "38510,38987"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/c6ca12b1c9d1e9bd3d210d5997c3a84d/transformed/core-1.10.1/res/values-v21/values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,17,18,19,360,361,362,363,568,571", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,1408,1472,1539,26377,26493,26619,26745,38992,39164", "endLines": "2,17,18,19,360,361,362,363,570,575", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,1467,1534,1598,26488,26614,26740,26868,39159,39511"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/b506d2377d150ff1ba498f2cff15408b/transformed/material-1.10.0/res/values-v21/values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,29,32,35,38,41,44,47,50,53,56,59,60,63,68,79,85,94,103,112,121,130,139,148,157,166,175,184,193,202,211,220,226,232,238,244,248,252,253,254,255,259,262,265,268,271,272,275,278,282,286", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,249,345,443,511,590,678,766,854,942,1029,1116,1203,1290,1386,1476,1572,1662,1755,1862,1967,2086,2211,2332,2545,2804,3075,3293,3525,3761,4011,4224,4433,4664,4865,4981,5151,5472,6501,6958,7462,7970,8479,8993,9498,10002,10507,11013,11515,12021,12530,13038,13537,14044,14552,14844,15138,15438,15738,16067,16408,16546,16690,16846,17239,17457,17679,17905,18121,18231,18401,18591,18832,19091", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,28,31,34,37,40,43,46,49,52,55,58,59,62,67,78,84,93,102,111,120,129,138,147,156,165,174,183,192,201,210,219,225,231,237,243,247,251,252,253,254,258,261,264,267,270,271,274,277,281,285,288", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,95,89,95,89,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "148,244,340,438,506,585,673,761,849,937,1024,1111,1198,1285,1381,1471,1567,1657,1750,1857,1962,2081,2206,2327,2540,2799,3070,3288,3520,3756,4006,4219,4428,4659,4860,4976,5146,5467,6496,6953,7457,7965,8474,8988,9493,9997,10502,11008,11510,12016,12525,13033,13532,14039,14547,14839,15133,15433,15733,16062,16403,16541,16685,16841,17234,17452,17674,17900,18116,18226,18396,18586,18827,19086,19263"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,20,21,22,23,97,98,99,100,102,103,104,107,110,205,208,211,214,220,223,226,293,296,297,300,305,316,364,373,382,391,400,409,418,427,436,445,454,463,472,481,490,499,505,511,517,523,527,531,532,533,534,538,541,544,547,576,577,580,583,587,591", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "173,271,367,463,561,629,708,796,884,972,1060,1147,1234,1321,1603,1699,1789,1885,8161,8254,8361,8466,8688,8813,8934,9147,9406,15587,15805,16037,16273,16722,16935,17144,21931,22132,22248,22418,22739,23768,26873,27377,27885,28394,28908,29413,29917,30422,30928,31430,31936,32445,32953,33452,33959,34467,34759,35053,35353,35653,35982,36323,36461,36605,36761,37154,37372,37594,37820,39516,39626,39796,39986,40227,40486", "endLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,20,21,22,23,97,98,99,100,102,103,106,109,112,207,210,213,216,222,225,228,295,296,299,304,315,321,372,381,390,399,408,417,426,435,444,453,462,471,480,489,498,504,510,516,522,526,530,531,532,533,537,540,543,546,549,576,579,582,586,590,593", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,95,89,95,89,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "266,362,458,556,624,703,791,879,967,1055,1142,1229,1316,1403,1694,1784,1880,1970,8249,8356,8461,8580,8808,8929,9142,9401,9672,15800,16032,16268,16518,16930,17139,17370,22127,22243,22413,22734,23763,24220,27372,27880,28389,28903,29408,29912,30417,30923,31425,31931,32440,32948,33447,33954,34462,34754,35048,35348,35648,35977,36318,36456,36600,36756,37149,37367,37589,37815,38031,39621,39791,39981,40222,40481,40658"}}]}]}