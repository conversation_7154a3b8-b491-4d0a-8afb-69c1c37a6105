{"logs": [{"outputFile": "com.hailiang.textcorrection.test.textcorrection-mergeDebugAndroidTestResources-78:/values-gu/values-gu.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/b506d2377d150ff1ba498f2cff15408b/transformed/material-1.10.0/res/values-gu/values-gu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,342,414,496,602,700,799,919,1003,1066,1157,1224,1283,1373,1436,1501,1565,1634,1696,1750,1865,1923,1984,2038,2111,2238,2324,2408,2541,2616,2692,2825,2911,2992,3046,3098,3164,3237,3317,3402,3482,3553,3629,3708,3777,3884,3980,4058,4153,4249,4323,4398,4497,4548,4630,4697,4784,4874,4936,5000,5063,5130,5232,5337,5434,5536,5594,5650", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,73,71,81,105,97,98,119,83,62,90,66,58,89,62,64,63,68,61,53,114,57,60,53,72,126,85,83,132,74,75,132,85,80,53,51,65,72,79,84,79,70,75,78,68,106,95,77,94,95,73,74,98,50,81,66,86,89,61,63,62,66,101,104,96,101,57,55,77", "endOffsets": "263,337,409,491,597,695,794,914,998,1061,1152,1219,1278,1368,1431,1496,1560,1629,1691,1745,1860,1918,1979,2033,2106,2233,2319,2403,2536,2611,2687,2820,2906,2987,3041,3093,3159,3232,3312,3397,3477,3548,3624,3703,3772,3879,3975,4053,4148,4244,4318,4393,4492,4543,4625,4692,4779,4869,4931,4995,5058,5125,5227,5332,5429,5531,5589,5645,5723"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,90,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3448,3522,3594,3676,3782,4598,4697,4817,9070,9211,9302,9556,9615,9705,9768,9833,9897,9966,10028,10082,10197,10255,10316,10370,10443,10570,10656,10740,10873,10948,11024,11157,11243,11324,11378,11430,11496,11569,11649,11734,11814,11885,11961,12040,12109,12216,12312,12390,12485,12581,12655,12730,12829,12880,12962,13029,13116,13206,13268,13332,13395,13462,13564,13669,13766,13868,13926,14292", "endLines": "5,37,38,39,40,41,49,50,51,90,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,156", "endColumns": "12,73,71,81,105,97,98,119,83,62,90,66,58,89,62,64,63,68,61,53,114,57,60,53,72,126,85,83,132,74,75,132,85,80,53,51,65,72,79,84,79,70,75,78,68,106,95,77,94,95,73,74,98,50,81,66,86,89,61,63,62,66,101,104,96,101,57,55,77", "endOffsets": "313,3517,3589,3671,3777,3875,4692,4812,4896,9128,9297,9364,9610,9700,9763,9828,9892,9961,10023,10077,10192,10250,10311,10365,10438,10565,10651,10735,10868,10943,11019,11152,11238,11319,11373,11425,11491,11564,11644,11729,11809,11880,11956,12035,12104,12211,12307,12385,12480,12576,12650,12725,12824,12875,12957,13024,13111,13201,13263,13327,13390,13457,13559,13664,13761,13863,13921,13977,14365"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/cd8107e63a7f52f8260c7fe730ff25cc/transformed/appcompat-1.6.1/res/values-gu/values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,425,529,636,723,823,943,1021,1098,1189,1282,1377,1471,1571,1664,1759,1853,1944,2035,2115,2221,2322,2419,2528,2628,2738,2898,14617", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "420,524,631,718,818,938,1016,1093,1184,1277,1372,1466,1566,1659,1754,1848,1939,2030,2110,2216,2317,2414,2523,2623,2733,2893,2996,14693"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/c6ca12b1c9d1e9bd3d210d5997c3a84d/transformed/core-1.10.1/res/values-gu/values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "42,43,44,45,46,47,48,163", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3880,3974,4077,4174,4276,4378,4476,14856", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "3969,4072,4169,4271,4373,4471,4593,14952"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/4fc144a24dbbc55f2448d27c72f9c72c/transformed/jetified-ui-release/res/values-gu/values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,372,471,558,644,745,832,918,986,1055,1138,1221,1296,1372,1438", "endColumns": "91,81,92,98,86,85,100,86,85,67,68,82,82,74,75,65,115", "endOffsets": "192,274,367,466,553,639,740,827,913,981,1050,1133,1216,1291,1367,1433,1549"}, "to": {"startLines": "52,53,86,87,89,94,95,152,153,154,155,157,158,161,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4901,4993,8714,8807,8983,9369,9455,13982,14069,14155,14223,14370,14453,14698,15051,15127,15193", "endColumns": "91,81,92,98,86,85,100,86,85,67,68,82,82,74,75,65,115", "endOffsets": "4988,5070,8802,8901,9065,9450,9551,14064,14150,14218,14287,14448,14531,14768,15122,15188,15304"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/94b89705b0416f05ff2804fe35f6c12e/transformed/jetified-material3-1.1.1/res/values-gu/values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,273,394,502,581,676,789,924,1040,1178,1259,1359,1449,1545,1655,1779,1884,2015,2143,2269,2444,2566,2684,2807,2939,3030,3122,3245,3371,3468,3569,3672,3802,3939,4044,4141,4218,4296,4377,4460,4554,4630,4710,4807,4906,5000,5096,5179,5281,5376,5474,5588,5664,5760", "endColumns": "109,107,120,107,78,94,112,134,115,137,80,99,89,95,109,123,104,130,127,125,174,121,117,122,131,90,91,122,125,96,100,102,129,136,104,96,76,77,80,82,93,75,79,96,98,93,95,82,101,94,97,113,75,95,89", "endOffsets": "160,268,389,497,576,671,784,919,1035,1173,1254,1354,1444,1540,1650,1774,1879,2010,2138,2264,2439,2561,2679,2802,2934,3025,3117,3240,3366,3463,3564,3667,3797,3934,4039,4136,4213,4291,4372,4455,4549,4625,4705,4802,4901,4995,5091,5174,5276,5371,5469,5583,5659,5755,5845"}, "to": {"startLines": "33,34,35,36,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,88,91,159,162,164,168,169,170,171,172,173,174,175,176,177,178,179,180,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3001,3111,3219,3340,5075,5154,5249,5362,5497,5613,5751,5832,5932,6022,6118,6228,6352,6457,6588,6716,6842,7017,7139,7257,7380,7512,7603,7695,7818,7944,8041,8142,8245,8375,8512,8617,8906,9133,14536,14773,14957,15309,15385,15465,15562,15661,15755,15851,15934,16036,16131,16229,16343,16419,16515", "endColumns": "109,107,120,107,78,94,112,134,115,137,80,99,89,95,109,123,104,130,127,125,174,121,117,122,131,90,91,122,125,96,100,102,129,136,104,96,76,77,80,82,93,75,79,96,98,93,95,82,101,94,97,113,75,95,89", "endOffsets": "3106,3214,3335,3443,5149,5244,5357,5492,5608,5746,5827,5927,6017,6113,6223,6347,6452,6583,6711,6837,7012,7134,7252,7375,7507,7598,7690,7813,7939,8036,8137,8240,8370,8507,8612,8709,8978,9206,14612,14851,15046,15380,15460,15557,15656,15750,15846,15929,16031,16126,16224,16338,16414,16510,16600"}}]}]}