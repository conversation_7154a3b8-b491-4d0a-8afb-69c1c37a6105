{"logs": [{"outputFile": "com.hailiang.textcorrection.test.textcorrection-mergeDebugAndroidTestResources-78:/values-ne/values-ne.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/cd8107e63a7f52f8260c7fe730ff25cc/transformed/appcompat-1.6.1/res/values-ne/values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2247,2360,2470,2587,2754,2865", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2242,2355,2465,2582,2749,2860,2940"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "312,421,532,640,731,838,958,1042,1121,1212,1305,1400,1494,1594,1687,1782,1876,1967,2058,2144,2257,2358,2454,2567,2677,2794,2961,14967", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "416,527,635,726,833,953,1037,1116,1207,1300,1395,1489,1589,1682,1777,1871,1962,2053,2139,2252,2353,2449,2562,2672,2789,2956,3067,15042"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/94b89705b0416f05ff2804fe35f6c12e/transformed/jetified-material3-1.1.1/res/values-ne/values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,295,407,531,615,706,815,955,1072,1221,1301,1405,1499,1594,1702,1822,1931,2073,2212,2342,2503,2629,2777,2931,3057,3152,3243,3355,3475,3575,3691,3796,3937,4081,4187,4290,4361,4445,4532,4618,4721,4797,4878,4975,5080,5171,5270,5353,5460,5555,5655,5786,5862,5962", "endColumns": "124,114,111,123,83,90,108,139,116,148,79,103,93,94,107,119,108,141,138,129,160,125,147,153,125,94,90,111,119,99,115,104,140,143,105,102,70,83,86,85,102,75,80,96,104,90,98,82,106,94,99,130,75,99,89", "endOffsets": "175,290,402,526,610,701,810,950,1067,1216,1296,1400,1494,1589,1697,1817,1926,2068,2207,2337,2498,2624,2772,2926,3052,3147,3238,3350,3470,3570,3686,3791,3932,4076,4182,4285,4356,4440,4527,4613,4716,4792,4873,4970,5075,5166,5265,5348,5455,5550,5650,5781,5857,5957,6047"}, "to": {"startLines": "33,34,35,36,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,88,91,159,162,164,168,169,170,171,172,173,174,175,176,177,178,179,180,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3072,3197,3312,3424,5199,5283,5374,5483,5623,5740,5889,5969,6073,6167,6262,6370,6490,6599,6741,6880,7010,7171,7297,7445,7599,7725,7820,7911,8023,8143,8243,8359,8464,8605,8749,8855,9149,9372,14880,15124,15311,15683,15759,15840,15937,16042,16133,16232,16315,16422,16517,16617,16748,16824,16924", "endColumns": "124,114,111,123,83,90,108,139,116,148,79,103,93,94,107,119,108,141,138,129,160,125,147,153,125,94,90,111,119,99,115,104,140,143,105,102,70,83,86,85,102,75,80,96,104,90,98,82,106,94,99,130,75,99,89", "endOffsets": "3192,3307,3419,3543,5278,5369,5478,5618,5735,5884,5964,6068,6162,6257,6365,6485,6594,6736,6875,7005,7166,7292,7440,7594,7720,7815,7906,8018,8138,8238,8354,8459,8600,8744,8850,8953,9215,9451,14962,15205,15409,15754,15835,15932,16037,16128,16227,16310,16417,16512,16612,16743,16819,16919,17009"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/4fc144a24dbbc55f2448d27c72f9c72c/transformed/jetified-ui-release/res/values-ne/values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,294,388,485,571,653,749,836,922,988,1054,1144,1237,1314,1395,1463", "endColumns": "98,89,93,96,85,81,95,86,85,65,65,89,92,76,80,67,119", "endOffsets": "199,289,383,480,566,648,744,831,917,983,1049,1139,1232,1309,1390,1458,1578"}, "to": {"startLines": "52,53,86,87,89,94,95,152,153,154,155,157,158,161,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5010,5109,8958,9052,9220,9618,9700,14308,14395,14481,14547,14697,14787,15047,15414,15495,15563", "endColumns": "98,89,93,96,85,81,95,86,85,65,65,89,92,76,80,67,119", "endOffsets": "5104,5194,9047,9144,9301,9695,9791,14390,14476,14542,14608,14782,14875,15119,15490,15558,15678"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/b506d2377d150ff1ba498f2cff15408b/transformed/material-1.10.0/res/values-ne/values-ne.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,262,351,439,521,616,705,807,917,1004,1070,1166,1232,1293,1398,1462,1534,1592,1666,1728,1782,1895,1955,2016,2075,2153,2277,2358,2443,2579,2660,2743,2874,2957,3043,3105,3159,3225,3302,3381,3469,3552,3621,3697,3778,3846,3950,4041,4119,4212,4309,4383,4462,4560,4620,4708,4774,4862,4950,5012,5080,5143,5209,5314,5420,5515,5620,5686,5744", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,88,87,81,94,88,101,109,86,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,84,135,80,82,130,82,85,61,53,65,76,78,87,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83", "endOffsets": "257,346,434,516,611,700,802,912,999,1065,1161,1227,1288,1393,1457,1529,1587,1661,1723,1777,1890,1950,2011,2070,2148,2272,2353,2438,2574,2655,2738,2869,2952,3038,3100,3154,3220,3297,3376,3464,3547,3616,3692,3773,3841,3945,4036,4114,4207,4304,4378,4457,4555,4615,4703,4769,4857,4945,5007,5075,5138,5204,5309,5415,5510,5615,5681,5739,5823"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,90,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3548,3637,3725,3807,3902,4711,4813,4923,9306,9456,9552,9796,9857,9962,10026,10098,10156,10230,10292,10346,10459,10519,10580,10639,10717,10841,10922,11007,11143,11224,11307,11438,11521,11607,11669,11723,11789,11866,11945,12033,12116,12185,12261,12342,12410,12514,12605,12683,12776,12873,12947,13026,13124,13184,13272,13338,13426,13514,13576,13644,13707,13773,13878,13984,14079,14184,14250,14613", "endLines": "5,37,38,39,40,41,49,50,51,90,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,156", "endColumns": "12,88,87,81,94,88,101,109,86,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,84,135,80,82,130,82,85,61,53,65,76,78,87,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83", "endOffsets": "307,3632,3720,3802,3897,3986,4808,4918,5005,9367,9547,9613,9852,9957,10021,10093,10151,10225,10287,10341,10454,10514,10575,10634,10712,10836,10917,11002,11138,11219,11302,11433,11516,11602,11664,11718,11784,11861,11940,12028,12111,12180,12256,12337,12405,12509,12600,12678,12771,12868,12942,13021,13119,13179,13267,13333,13421,13509,13571,13639,13702,13768,13873,13979,14074,14179,14245,14303,14692"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/c6ca12b1c9d1e9bd3d210d5997c3a84d/transformed/core-1.10.1/res/values-ne/values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "42,43,44,45,46,47,48,163", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3991,4094,4197,4299,4405,4503,4603,15210", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "4089,4192,4294,4400,4498,4598,4706,15306"}}]}]}