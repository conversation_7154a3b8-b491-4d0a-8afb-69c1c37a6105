{"logs": [{"outputFile": "com.hailiang.textcorrection.test.textcorrection-mergeDebugAndroidTestResources-78:/values-el/values-el.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/cd8107e63a7f52f8260c7fe730ff25cc/transformed/appcompat-1.6.1/res/values-el/values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "320,438,549,666,751,857,980,1069,1154,1245,1338,1433,1527,1627,1720,1815,1912,2003,2094,2179,2290,2399,2501,2612,2722,2830,3001,15142", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "433,544,661,746,852,975,1064,1149,1240,1333,1428,1522,1622,1715,1810,1907,1998,2089,2174,2285,2394,2496,2607,2717,2825,2996,3096,15223"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/94b89705b0416f05ff2804fe35f6c12e/transformed/jetified-material3-1.1.1/res/values-el/values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,295,405,523,603,702,819,966,1090,1244,1330,1426,1521,1622,1736,1858,1959,2097,2229,2369,2545,2679,2795,2919,3040,3136,3231,3363,3496,3598,3700,3806,3945,4094,4204,4305,4388,4467,4553,4638,4737,4813,4892,4987,5085,5178,5272,5355,5457,5552,5649,5766,5842,5944", "endColumns": "119,119,109,117,79,98,116,146,123,153,85,95,94,100,113,121,100,137,131,139,175,133,115,123,120,95,94,131,132,101,101,105,138,148,109,100,82,78,85,84,98,75,78,94,97,92,93,82,101,94,96,116,75,101,102", "endOffsets": "170,290,400,518,598,697,814,961,1085,1239,1325,1421,1516,1617,1731,1853,1954,2092,2224,2364,2540,2674,2790,2914,3035,3131,3226,3358,3491,3593,3695,3801,3940,4089,4199,4300,4383,4462,4548,4633,4732,4808,4887,4982,5080,5173,5267,5350,5452,5547,5644,5761,5837,5939,6042"}, "to": {"startLines": "33,34,35,36,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,88,91,159,162,164,168,169,170,171,172,173,174,175,176,177,178,179,180,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3101,3221,3341,3451,5236,5316,5415,5532,5679,5803,5957,6043,6139,6234,6335,6449,6571,6672,6810,6942,7082,7258,7392,7508,7632,7753,7849,7944,8076,8209,8311,8413,8519,8658,8807,8917,9224,9465,15056,15303,15489,15860,15936,16015,16110,16208,16301,16395,16478,16580,16675,16772,16889,16965,17067", "endColumns": "119,119,109,117,79,98,116,146,123,153,85,95,94,100,113,121,100,137,131,139,175,133,115,123,120,95,94,131,132,101,101,105,138,148,109,100,82,78,85,84,98,75,78,94,97,92,93,82,101,94,96,116,75,101,102", "endOffsets": "3216,3336,3446,3564,5311,5410,5527,5674,5798,5952,6038,6134,6229,6330,6444,6566,6667,6805,6937,7077,7253,7387,7503,7627,7748,7844,7939,8071,8204,8306,8408,8514,8653,8802,8912,9013,9302,9539,15137,15383,15583,15931,16010,16105,16203,16296,16390,16473,16575,16670,16767,16884,16960,17062,17165"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/4fc144a24dbbc55f2448d27c72f9c72c/transformed/jetified-ui-release/res/values-el/values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,290,391,496,588,669,763,852,942,1012,1080,1161,1243,1318,1397,1467", "endColumns": "98,85,100,104,91,80,93,88,89,69,67,80,81,74,78,69,122", "endOffsets": "199,285,386,491,583,664,758,847,937,1007,1075,1156,1238,1313,1392,1462,1585"}, "to": {"startLines": "52,53,86,87,89,94,95,152,153,154,155,157,158,161,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5051,5150,9018,9119,9307,9726,9807,14485,14574,14664,14734,14893,14974,15228,15588,15667,15737", "endColumns": "98,85,100,104,91,80,93,88,89,69,67,80,81,74,78,69,122", "endOffsets": "5145,5231,9114,9219,9394,9802,9896,14569,14659,14729,14797,14969,15051,15298,15662,15732,15855"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/c6ca12b1c9d1e9bd3d210d5997c3a84d/transformed/core-1.10.1/res/values-el/values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "42,43,44,45,46,47,48,163", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4011,4109,4212,4312,4415,4523,4629,15388", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "4104,4207,4307,4410,4518,4624,4741,15484"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/b506d2377d150ff1ba498f2cff15408b/transformed/material-1.10.0/res/values-el/values-el.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,358,444,529,625,712,814,931,1017,1083,1183,1265,1328,1419,1482,1547,1609,1678,1740,1794,1932,1989,2050,2104,2177,2330,2415,2499,2638,2719,2804,2945,3035,3121,3176,3227,3293,3371,3456,3541,3624,3696,3776,3856,3927,4034,4126,4198,4295,4392,4466,4540,4642,4698,4785,4857,4945,5037,5099,5163,5226,5296,5412,5521,5630,5735,5794,5849", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,87,85,84,95,86,101,116,85,65,99,81,62,90,62,64,61,68,61,53,137,56,60,53,72,152,84,83,138,80,84,140,89,85,54,50,65,77,84,84,82,71,79,79,70,106,91,71,96,96,73,73,101,55,86,71,87,91,61,63,62,69,115,108,108,104,58,54,90", "endOffsets": "265,353,439,524,620,707,809,926,1012,1078,1178,1260,1323,1414,1477,1542,1604,1673,1735,1789,1927,1984,2045,2099,2172,2325,2410,2494,2633,2714,2799,2940,3030,3116,3171,3222,3288,3366,3451,3536,3619,3691,3771,3851,3922,4029,4121,4193,4290,4387,4461,4535,4637,4693,4780,4852,4940,5032,5094,5158,5221,5291,5407,5516,5625,5730,5789,5844,5935"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,90,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3569,3657,3743,3828,3924,4746,4848,4965,9399,9544,9644,9901,9964,10055,10118,10183,10245,10314,10376,10430,10568,10625,10686,10740,10813,10966,11051,11135,11274,11355,11440,11581,11671,11757,11812,11863,11929,12007,12092,12177,12260,12332,12412,12492,12563,12670,12762,12834,12931,13028,13102,13176,13278,13334,13421,13493,13581,13673,13735,13799,13862,13932,14048,14157,14266,14371,14430,14802", "endLines": "5,37,38,39,40,41,49,50,51,90,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,156", "endColumns": "12,87,85,84,95,86,101,116,85,65,99,81,62,90,62,64,61,68,61,53,137,56,60,53,72,152,84,83,138,80,84,140,89,85,54,50,65,77,84,84,82,71,79,79,70,106,91,71,96,96,73,73,101,55,86,71,87,91,61,63,62,69,115,108,108,104,58,54,90", "endOffsets": "315,3652,3738,3823,3919,4006,4843,4960,5046,9460,9639,9721,9959,10050,10113,10178,10240,10309,10371,10425,10563,10620,10681,10735,10808,10961,11046,11130,11269,11350,11435,11576,11666,11752,11807,11858,11924,12002,12087,12172,12255,12327,12407,12487,12558,12665,12757,12829,12926,13023,13097,13171,13273,13329,13416,13488,13576,13668,13730,13794,13857,13927,14043,14152,14261,14366,14425,14480,14888"}}]}]}