technology_indicators:
  # Android & AGP 版本识别
  - pattern: "**/build.gradle"
    indicators:
      - technology: "Android"
        confidence: 0.95
        version_extract: "com.android.tools.build:gradle:(.*)"

  # Kotlin 版本识别
  - pattern: "**/build.gradle.kts"
    indicators:
      - technology: "Kotlin"
        confidence: 0.90
        version_extract: "kotlin\\..*version.*\"(.*)\""

  # Gradle JVM 参数识别
  - pattern: "**/gradle.properties"
    indicators:
      - technology: "BuildConfig"
        confidence: 0.85
        metadata_extract:
          gradle_jvm_options: "-Xmx.*-XX:MaxMetaspaceSize.*"

  # 是否启用 AndroidX
  - pattern: "**/gradle.properties"
    indicators:
      - technology: "AndroidX"
        confidence: 0.80
        flag_present: "android.useAndroidX=true"