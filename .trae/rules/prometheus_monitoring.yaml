# Prometheus监控规则示例

alerts:
  - alert: HighErrorRate
    expr: "rate(ai_fix_errors_total[5m]) > 0.1"
    for: "10m"
    labels:
      severity: critical
    annotations:
      summary: "AI 辅助修复错误率过高"

metrics:
  - name: ai_fix_attempts_total
    help: "AI 尝试修复代码的总次数"
    type: counter

  - name: ai_fix_success_total
    help: "AI 成功修复代码的总次数"
    type: counter

  - name: ai_fix_errors_total
    help: "AI 修复过程中出错的总次数"
    type: counter

  - name: ai_code_change_duration_seconds
    help: "AI 修改代码所耗时间（秒）"
    type: histogram

scrape_configs:
  - job_name: 'ai-agent'
    static_configs:
      - targets: ['localhost:8080']