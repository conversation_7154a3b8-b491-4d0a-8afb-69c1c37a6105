# 综合项目治理规则体系

## 1. 接口字段变更治理规则
interface_field_modification:
  pattern: "**/api/*ApiService.kt"
  operations: [MODIFY, CREATE]
  actions:
    static_analysis:
      dependency_scope: CALL_GRAPH
      max_depth: 3
    validation:
      required_checks:
        - backward_compatibility
        - deprecated_field_migration
    auto_sync:
      field_mapping: "config/field_mappings.yaml"
  outputs:
    - impact_report
    - migration_guide

## 2. 功能实现/错误修改核心规则矩阵
feature_implementation_rules:
  stages:
    - name: 需求解析
      rule: 必须包含预期行为描述、当前实际行为、复现步骤
    - name: 影响分析
      rule: 自动识别调用链路相关模块（深度3）
    - name: 方案设计
      rule: 相似功能实现检索（余弦相似度0.7）
    - name: 代码生成
      rule: 禁止使用危险方法如 eval()/System.exec()
    - name: 测试验证
      rule: 包含 null/空值/越界值测试用例

## 3. 智能修改验证规则链
change_validation_chain:
  rules:
    - syntax_check
    - type_safety
    - dependency_consistency
    - security_policy
    - performance_impact

## 4. 错误修正决策树规则
error_fix_decision_tree:
  rules:
    null_pointer_exception:
      language: kotlin
      fixes:
        - safe_call
        - optional_chaining
    type_mismatch_error:
      language: java/kotlin
      fixes:
        - cast_type
        - use_generic_type

## 5. 变更影响度评估公式
impact_score_formula:
  formula: |
    Impact Score = 
      (API_CHANGE * 3) + 
      (DATA_SCHEMA_CHANGE * 2) + 
      (UI_CHANGE * 1) -
      (TEST_COVERAGE * 0.5)
  thresholds:
    - score: ">=5"
      action: "需要架构评审"
    - score: "3<=score<5"
      action: "需要团队审核"
    - score: "<3"
      action: "可自动合并"

## 6. AI 行为约束规则
ai_behavior_constraints:
  forbidden_zones:
    - "/database/migrations/**"
    - "/proto/**/*.proto"
    - "**/generated/**"
  rollback_mechanism:
    enabled: true
    patch_dir: ".lingma/patches/"

## 7. 人机协作流程规则
collaboration_flow:
  steps:
    - type: auto
      when: confidence > 0.9
    - type: suggestion
      when: "0.6 < confidence <= 0.9"
      options:
        max_selections: 3
    - type: alert
      when: confidence <= 0.6
      required_approvers:
        min: 1