---
title: 项目架构与技术栈
type: note
permalink: project-info/项目架构与技术栈
---

## 项目架构

当前项目采用Android应用架构，可能基于MVVM或MVP模式。

### 模块说明
- `app`: 主应用程序模块，包含UI布局和业务逻辑。
- `common`: 提供通用功能和资源。
- `data`: 数据处理模块。
- `database`: 使用Room数据库管理本地数据。
- `textcorrection`: 文本校正模块，可能包含自然语言处理逻辑。

## 技术栈

- **前端**: Android原生UI，结合XML布局文件。
- **数据库**: Room数据库。
- **框架**: Kotlin语言，Android Jetpack组件。

### 构建配置
- AndroidX支持库启用：`android.useAndroidX=true`
- Jetifier工具启用：`android.enableJetifier=true`
- Gradle JVM参数设置为：`-Xmx4096m -XX:MaxMetaspaceSize=1024m`

## 功能概述

- 英语作文相关功能，包括作文检查、AI评判、练习等。
- 提供拍照作文功能。
- 包含多个Fragment界面，如作文AI评判、作文检查等。
- 支持Lottie动画效果。