package com.hailiang.composition.database.pojo

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey


@Entity(tableName = "composition_work_image_answer")
data class CompositionPictureAnswerInfoPo(

    @PrimaryKey(autoGenerate = true)
    val id: Long?,

    @ColumnInfo(name = "work_id")
    val workId: Long,

    @ColumnInfo(name = "work_state_id")
    val workStateId: Long,

    @ColumnInfo(name = "image_answer_list")
    val imageAnswerList: String?,

    @ColumnInfo(name = "last_image_answer_list")
    val lastImageAnswerList: String?,
)