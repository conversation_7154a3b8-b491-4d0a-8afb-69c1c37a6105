package com.hailiang.composition.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.hailiang.composition.database.pojo.CompositionPictureAnswerInfoPo
import kotlinx.coroutines.flow.Flow

@Dao
interface CompositionPictureAnswerInfoDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertOrUpdate(compositionPictureAnswer: CompositionPictureAnswerInfoPo): Long


    @Query(
        "UPDATE composition_work_image_answer SET last_image_answer_list= :lastImageAnswerList WHERE work_id = :workId " + "AND work_state_id = :workStateId"
    )
    fun updateByWorkStateId(workId: Long, workStateId: Long, lastImageAnswerList: String?)

    /**
     * 根据作业ID查询作答记录
     * @param workId 作业ID
     * @return 学生作答信息
     */
    @Query("SELECT * FROM composition_work_image_answer WHERE work_id = :workId AND work_state_id = :workStateId")
    fun queryByWorkId(workId: Long, workStateId: Long): CompositionPictureAnswerInfoPo?


    @Query("SELECT * FROM composition_work_image_answer WHERE work_id = :workId AND work_state_id = :workStateId")
    fun queryByWorkIdWithFlow(workId: Long, workStateId: Long): Flow<CompositionPictureAnswerInfoPo?>
}