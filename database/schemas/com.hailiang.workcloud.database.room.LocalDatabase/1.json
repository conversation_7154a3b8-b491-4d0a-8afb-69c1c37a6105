{"formatVersion": 1, "database": {"version": 1, "identityHash": "e5bb3e8dae2ed5d6068927a4d1af183f", "entities": [{"tableName": "handwrite_history", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `work_id` INTEGER NOT NULL, `question_id` INTEGER NOT NULL, `sub_question_id` INTEGER NOT NULL, `task_id` INTEGER NOT NULL, `material_id` INTEGER NOT NULL, `idx` INTEGER NOT NULL, `tag` TEXT NOT NULL, `strokes` TEXT NOT NULL, `update_time` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "workId", "columnName": "work_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "questionId", "columnName": "question_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "subQuestionId", "columnName": "sub_question_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "taskId", "columnName": "task_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "materialId", "columnName": "material_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "idx", "columnName": "idx", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "tag", "columnName": "tag", "affinity": "TEXT", "notNull": true}, {"fieldPath": "strokes", "columnName": "strokes", "affinity": "TEXT", "notNull": true}, {"fieldPath": "updateTime", "columnName": "update_time", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "composition_practice", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `work_id` INTEGER NOT NULL, `work_state_id` INTEGER NOT NULL, `second_practice_title` TEXT, `second_practice_content` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "workId", "columnName": "work_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "workStateId", "columnName": "work_state_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "secondPracticeTitle", "columnName": "second_practice_title", "affinity": "TEXT", "notNull": false}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "columnName": "second_practice_content", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "composition_work_answer", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `work_id` INTEGER NOT NULL, `answer_id` INTEGER, `first_answer` TEXT, `first_answer_detail` TEXT, `second_answer_detail` TEXT, `answer_edit_detail` TEXT, `image_answer_list` TEXT, `image_update_time` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "workId", "columnName": "work_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "answerId", "columnName": "answer_id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "firstAnswer", "columnName": "first_answer", "affinity": "TEXT", "notNull": false}, {"fieldPath": "studentFirstAnswerDetail", "columnName": "first_answer_detail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "secondAnswerDetail", "columnName": "second_answer_detail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "answerEditDetail", "columnName": "answer_edit_detail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "imageAnswerList", "columnName": "image_answer_list", "affinity": "TEXT", "notNull": false}, {"fieldPath": "imageUpdateTime", "columnName": "image_update_time", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "do_work_state", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `work_id` INTEGER NOT NULL, `work_state_id` INTEGER NOT NULL, `start_time` INTEGER NOT NULL, `submit_time` INTEGER NOT NULL, `time_counting` INTEGER NOT NULL, `state` INTEGER NOT NULL, `student_check_score` REAL NOT NULL, `error_message` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "workId", "columnName": "work_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "workStateId", "columnName": "work_state_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "startTime", "columnName": "start_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "submitTime", "columnName": "submit_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timeCounting", "columnName": "time_counting", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "state", "columnName": "state", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "studentCheckScore", "columnName": "student_check_score", "affinity": "REAL", "notNull": true}, {"fieldPath": "errorMessage", "columnName": "error_message", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "composition_check_detail", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `job_status` TEXT NOT NULL, `message` TEXT, `check_user_id` INTEGER NOT NULL, `work_id` INTEGER NOT NULL, `work_state_id` INTEGER NOT NULL, `ocr_title` TEXT, `ocr_content` TEXT, `comprehensive_judge` TEXT, `advice_list` TEXT, `allusion_list` TEXT, `score` REAL NOT NULL, `totalScore` INTEGER NOT NULL, `correct_word_list` TEXT, `update_time` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "jobStatus", "columnName": "job_status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "message", "columnName": "message", "affinity": "TEXT", "notNull": false}, {"fieldPath": "checkUserId", "columnName": "check_user_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "workId", "columnName": "work_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "workStateId", "columnName": "work_state_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "ocrTitle", "columnName": "ocr_title", "affinity": "TEXT", "notNull": false}, {"fieldPath": "o<PERSON>r<PERSON><PERSON><PERSON>", "columnName": "ocr_content", "affinity": "TEXT", "notNull": false}, {"fieldPath": "comprehensiveJudge", "columnName": "comprehensive_judge", "affinity": "TEXT", "notNull": false}, {"fieldPath": "adviceList", "columnName": "advice_list", "affinity": "TEXT", "notNull": false}, {"fieldPath": "allusionList", "columnName": "allusion_list", "affinity": "TEXT", "notNull": false}, {"fieldPath": "score", "columnName": "score", "affinity": "REAL", "notNull": true}, {"fieldPath": "totalScore", "columnName": "totalScore", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "correctWordList", "columnName": "correct_word_list", "affinity": "TEXT", "notNull": false}, {"fieldPath": "updateTime", "columnName": "update_time", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "composition_work_image_answer", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `work_id` INTEGER NOT NULL, `work_state_id` INTEGER NOT NULL, `image_answer_list` TEXT, `last_image_answer_list` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "workId", "columnName": "work_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "workStateId", "columnName": "work_state_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "imageAnswerList", "columnName": "image_answer_list", "affinity": "TEXT", "notNull": false}, {"fieldPath": "lastImageAnswerList", "columnName": "last_image_answer_list", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'e5bb3e8dae2ed5d6068927a4d1af183f')"]}}