pluginManagement {
    val localPropertiesFile = File(rootProject.projectDir, "local.properties")
    if (localPropertiesFile.exists()) {
        val properties = java.util.Properties()
        properties.load(java.io.DataInputStream(localPropertiesFile.inputStream()))
        extra["useLocalMaven"] = properties.getProperty("useLocalMaven", "false").toBoolean()
    } else {
        extra["useLocalMaven"] = false
    }
    println("pluginManagement useLocalMaven ${extra["useLocalMaven"]}")
    repositories {
        if (extra["useLocalMaven"] == true) {
            maven {
                isAllowInsecureProtocol = true
                url = uri("http://localhost:8081/repository/maven-public/")
            }
        }
        maven { url = uri("https://maven.aliyun.com/repository/gradle-plugin") }
        gradlePluginPortal()
        mavenLocal()
        google()
        mavenCentral()
    }
}
//
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    versionCatalogs {
        create("libs") {
            from(files("common/libs.versions.toml"))
        }
    }
    repositories {
        if (extra["useLocalMaven"] == true) {
            maven {
                isAllowInsecureProtocol = true
                url = uri("http://localhost:8081/repository/maven-public")
            }
        }


        google()
        mavenCentral()

        maven { url = uri("https://maven.aliyun.com/repository/public") }
        maven { url = uri("https://maven.aliyun.com/repository/jcenter") }
        maven { url = uri("https://maven.aliyun.com/repository/google") }
        mavenLocal()
        
        maven {
            isAllowInsecureProtocol = true
            url = uri("http://*********/repository/maven-snapshots/")
            credentials {
                username = "jyyjy"
                password = "jyyjy"
            }
        }
        maven {
            isAllowInsecureProtocol = true
            url = uri("http://*********/repository/maven-releases/")
            credentials {
                username = "jyyjy"
                password = "jyyjy"
            }
        }
        maven { url = uri("https://jitpack.io") }
        maven { url = uri("https://oss.sonatype.org/content/repositories/snapshots/") }

    }

    versionCatalogs {}
}


rootProject.name = "stu_en_composition"
include(":app")
include(":common")
include(":data")
include(":database")
