package com.hailiang.composition.data.bean

import com.hailiang.composition.data.enums.CorrectWordType
import com.hailiang.composition.data.enums.CorrectWordWay
import com.hailiang.composition.data.enums.JobStatus


/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/2/27 13:42
 */
data class CompositionCheckBean(
    /**
     * 批改id
     */
    val id: Long?,

    /**
     * 批改内容
     */
    var checkContent: CheckContent?,
) {

    // ----------------------------------------------------------------------

    /**
     * 批改内容
     */
    data class CheckContent(
        /**
         * 初始化 init 进行中 running 成功 success 失败 failed
         */
        val jobStatus: String?,
        /**
         * 额外信息，失败时的内容
         */
        var message: String?,
        /**
         * 评价任务结果，包含综合评价、点拨
         */
        val aiJudgeResult: AiJudge?,
        /**
         * ocr任务结果，即手写体识别结果
         */
        val ocrResult: OcrResult?,
        /**
         * 打分任务结果
         */
        val scoreResult: ScoreResult?,
        /**
         * 知识加油站任务结果
         */
        val allusionResult: AllusionResult?,

        /**
         * 是否二流
         */
        var isSubmitted: Boolean?,

        /**
         * 纠错内容
         */
        val correctResult: CorrectResult?,
    ) {
        fun getAiJobStatus(): JobStatus {
            return JobStatus.getEnumByValue(aiJudgeResult?.status)
        }

        fun getOcrJobStatus(): JobStatus {
            return JobStatus.getEnumByValue(ocrResult?.status)
        }

        fun getJobStatus(): JobStatus {
            return JobStatus.getEnumByValue(jobStatus)
        }

        fun getSecondComJudgeStatus(): JobStatus {
            return JobStatus.getEnumByValue(aiJudgeResult?.secondComJudgeStatus)
        }

        fun getAiJudgeJobStatus(): JobStatus {
            return JobStatus.getEnumByValue(aiJudgeResult?.status)
        }

        fun getScoreJobStatus(): JobStatus {
            return JobStatus.getEnumByValue(scoreResult?.status)
        }

        fun getAllusionJobStatus(): JobStatus {
            return JobStatus.getEnumByValue(allusionResult?.status)
        }

        fun getCorrectJobStatus(): JobStatus {
            return JobStatus.getEnumByValue(correctResult?.status)
        }
    }

    // ----------------------------------------------------------------------
    // AI评判数据类
    data class AiJudge(
        /**
         * 综合评价
         */
        val comprehensiveJudge: ComprehensiveJudge?,
        val secondComJudgeStatus: String?,
        /**
         * 点拨
         */
        val adviceList: List<Advice>?,
        val status: String?,
    )

    data class OcrResult(
        /**
         * OCR识别的内容
         */
        val content: List<String>?,
        /**
         * 图片相关的识别标注结果
         */
//        val imageResultList: List<ImageResult>?,

        val message: String?,
        val status: String?,
        /**
         * "2025-04-16 18:03:49"
         */
        val createTime: String?,
    )

    data class BoundingBox(
        /**
         * 图片中某一行的四个点坐标，每个点包含x，y轴，以逗号分隔
         */
        val boundingBox: String?,
        /**
         * 图片中某一行对应的文字内容
         */
        val text: String?,
        /**
         * 一行对应每个字坐标列表
         */
        val words: List<BoundingBox>?,
        val status: String?,
    )

    // ----------------------------------------------------------------------
    // 综合评价数据类
    data class ComprehensiveJudge(
        val advantage: String?,    // 文章优点
        val suggestion: String?,    // 提示建议
    )

    // 点评数据类
    data class Advice(
        val type: String?,         // 点评类型
        val typeName: String?,     // 点评类型名
        val sort: Int,            // 点评排序
        val judgeList: List<Judge>?,
    )

    // AI类型评价数据类
    data class Judge(
        val advantage: String?,    // 文章优点
        val suggestion: String?,   // 提示建议
        val content: String?,      // 针对内容
    )


    // 分数
    data class ScoreResult(
        val score: Float,
        val totalScore: Int,
        val status: String?,
    )

    // 知识加油站任务结果
    data class AllusionResult(
        /**
         * 典故列表（知识加油站）
         */
        val allusionList: List<Allusion>?,
        val status: String?,
    )

    // 典故数据类
    data class Allusion(
        /**
         * history-历史典故，reality-现实案例，RefinedSentence-名人名言/诗句 ,shortItem-短语,longSentence-长句
         */
        val type: String?,         // 典故类型
        val typeName: String?,     // 类型名
        val sort: Int,           // 排序
        val content: String?,      // 内容(markdown格式)
    )

    /**
     *  纠错内容
     */

    data class CorrectResult(
        val status: String?,
        val correctData: String?,
        val correctWords: List<CorrectWord>?,
    )


    data class CorrectWord(
        val originContent: String,
        val index: Int,
        val before: String,
        val after: String,
        val way: String,
        val type: String,
        val reason: String,
    ) {
        fun getCorrectWordWay(): CorrectWordWay? {
            return CorrectWordWay.getEnumByValue(way)
        }

        fun getCorrectWorType(): CorrectWordType? {
            return CorrectWordType.getEnumByValue(type)
        }
    }
}