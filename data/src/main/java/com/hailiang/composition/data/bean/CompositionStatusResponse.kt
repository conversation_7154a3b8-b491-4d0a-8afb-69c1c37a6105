package com.hailiang.composition.data.bean

import com.hailiang.composition.data.enums.JobStatus
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag

data class CompositionStatusResponse(
    //notYet 还不能流，ready 可以流，streaming 流中，finished 流完成，failed 流失败了
    var topicStreamStatus: String? = null,
    var streamStatus: String? = null,
    var secondComJudgeStatus: String? = null,
    var secondJudgeStreamStatus: String? = null,
    var firstCorrectStatus: String? = null,
    var firstCorrectStructStatus: String? = null, // 一流 当前流结束，ai纠错的结果还没有完成
    var secondCorrectStatus: String? = null,
    var secondCorrectStructStatus: String? = null,//二流 当前流结束，ai纠错的结果还没有完成
    var titleOcrResult: OcrResult? = null,
    var ocrResult: OcrResult? = null,
    var secondOcrResult: OcrResult? = null,
    var firstScoreStatus: String? = null,
    var secondScoreStatus: String? = null
) {

    /**
     * 准备中，还不能流，例如OCR识别中等
     */
    fun titlePreparing(): Boolean {
        return topicStreamStatus == AiStreamStatus.Preparing.value
    }

    /**
     * 可以生成流
     */
    fun titleIsReady(): Boolean {
        return topicStreamStatus == AiStreamStatus.Ready.value
    }

    fun titleIsRunning(): Boolean {
        return topicStreamStatus == AiStreamStatus.Running.value
    }

    fun titleIsFinished(): Boolean {
        return topicStreamStatus == AiStreamStatus.Finished.value
    }

    fun titleIsFailed(): Boolean {
        return topicStreamStatus == AiStreamStatus.Failed.value
    }


    /**
     *   一流纠错 准备中，还不能流，例如OCR识别中等
     */
    fun firstCorrectPreparing(): Boolean {
        return firstCorrectStatus == AiStreamStatus.Preparing.value
    }

    /**
     * 可以生成流
     */
    fun firstCorrectIsReady(): Boolean {
        return firstCorrectStatus == AiStreamStatus.Ready.value
    }

    fun firstCorrectIsRunning(): Boolean {
        return firstCorrectStatus == AiStreamStatus.Running.value
    }

    fun firstCorrectIsFinished(): Boolean {
        return firstCorrectStatus == AiStreamStatus.Finished.value
    }

    /**
     *  一流纠错的流已经结束了，此时等待纠错结果返回
     */
    fun firstCorrectStructLoading(): Boolean {
        return firstCorrectStructStatus ==
                AiStreamStatus.Running.value
    }

    fun firstCorrectIsFailed(): Boolean {
        return firstCorrectStatus == AiStreamStatus.Failed.value
    }

    /**
     * 准备中，还不能流，例如OCR识别中等
     */
    fun firstPreparing(): Boolean {
        return streamStatus == AiStreamStatus.Preparing.value
    }

    /**
     * 可以生成流
     */
    fun firstIsReady(): Boolean {
        return streamStatus == AiStreamStatus.Ready.value
    }

    fun firstIsRunning(): Boolean {
        return streamStatus == AiStreamStatus.Running.value
    }

    fun firstIsFinished(): Boolean {
        return streamStatus == AiStreamStatus.Finished.value
    }

    fun firstIsFailed(): Boolean {
        return streamStatus == AiStreamStatus.Failed.value
    }


    /**
     *   一流纠错 准备中，还不能流，例如OCR识别中等
     */
    fun secondCorrectPreparing(): Boolean {
        return secondCorrectStatus == AiStreamStatus.Preparing.value
    }

    /**
     * 可以生成流
     */
    fun secondCorrectIsReady(): Boolean {
        return secondCorrectStatus == AiStreamStatus.Ready.value
    }

    fun secondCorrectIsRunning(): Boolean {
        return secondCorrectStatus == AiStreamStatus.Running.value
    }

    fun secondCorrectIsFinished(): Boolean {
        return secondCorrectStatus == AiStreamStatus.Finished.value
    }

    fun secondCorrectStructLoading(): Boolean {
        return secondCorrectStructStatus ==
                AiStreamStatus.Running.value
    }

    fun secondCorrectIsFailed(): Boolean {
        return secondCorrectStatus == AiStreamStatus.Failed.value
    }


    /**
     * 准备中，还不能流，例如OCR识别中等
     */
    fun secondPreparing(): Boolean {
        return secondJudgeStreamStatus == AiStreamStatus.Preparing.value
    }

    /**
     * 可以生成流
     */
    fun secondIsReady(): Boolean {
        return secondJudgeStreamStatus == AiStreamStatus.Ready.value
    }

    fun secondIsRunning(): Boolean {
        return secondJudgeStreamStatus == AiStreamStatus.Running.value
    }

    fun secondIsFinished(): Boolean {
        return secondJudgeStreamStatus == AiStreamStatus.Finished.value
    }

    fun secondIsFailed(): Boolean {
        return secondJudgeStreamStatus == AiStreamStatus.Failed.value
    }

    // ----------------------------------------------------------------------
    fun getOcrJobStatus(): JobStatus {
        return JobStatus.getEnumByValue(ocrResult?.status)
    }

    fun getSecondOcrJobStatus(): JobStatus {
        return JobStatus.getEnumByValue(secondOcrResult?.status)
    }

    fun getTitleOcrStatus(): JobStatus {
        return JobStatus.getEnumByValue(titleOcrResult?.status)
    }

    fun isEmpty(): Boolean {
        return topicStreamStatus.isNullOrEmpty() && streamStatus.isNullOrEmpty() && secondJudgeStreamStatus.isNullOrEmpty() && titleOcrResult == null && ocrResult == null
    }

    fun prepared(): Boolean {
        return getOcrJobStatus().isSuccess() && getTitleOcrStatus().isSuccess()
    }

    // ----------------------------------------------------------------------
    fun getRunningType(): AiStreamType {
        return when {
            titleIsRunning() -> {
                HLog.d(HTag.TAG, "审题立意中")
                AiStreamType.Title
            }

            firstCorrectIsRunning() -> {
                HLog.d(HTag.TAG, "一流纠错中")
                AiStreamType.FirstCorrection
            }

            firstIsRunning() -> {
                HLog.d(HTag.TAG, "第一次批改中")
                AiStreamType.First
            }

            secondCorrectIsRunning() -> {
                HLog.d(HTag.TAG, "二次纠错中")
                AiStreamType.SecondCorrection
            }

            secondIsRunning() -> {
                HLog.d(HTag.TAG, "二次批改中")
                AiStreamType.Second
            }

            else -> {
                AiStreamType.Default
            }
        }
    }

    fun getPreparingType(): AiStreamType {
        return when {
            titlePreparing() -> {
                HLog.d(HTag.TAG, "审题立意准备中")
                AiStreamType.Title
            }

            firstCorrectPreparing() -> {
                HLog.d(HTag.TAG, "一流纠错准备中")
                AiStreamType.FirstCorrection
            }

            firstPreparing() -> {
                HLog.d(HTag.TAG, "一流准备中")
                AiStreamType.First
            }


            secondCorrectPreparing() -> {
                HLog.d(HTag.TAG, "二次纠错准备中")
                AiStreamType.SecondCorrection
            }

            secondPreparing() -> {
                HLog.d(HTag.TAG, "二流准备中")
                AiStreamType.Second
            }

            else -> {
                AiStreamType.Default
            }
        }
    }

    fun getReadyType(): AiStreamType {
        return when {
            titleIsReady() -> {
                HLog.d(HTag.TAG, "审题立意准备完毕")
                AiStreamType.Title
            }

            firstCorrectIsReady() -> {
                HLog.d(HTag.TAG, "一流纠错准备完毕")
                AiStreamType.FirstCorrection
            }

            firstIsReady() -> {
                HLog.d(HTag.TAG, "一流准备完毕")
                AiStreamType.First
            }

            secondCorrectIsReady() -> {
                HLog.d(HTag.TAG, "二次纠错准备完毕")
                AiStreamType.SecondCorrection
            }

            secondIsReady() -> {
                HLog.d(HTag.TAG, "二流准备完毕")
                AiStreamType.Second
            }

            else -> {
                AiStreamType.Default
            }
        }
    }

    fun getFinishType(): AiStreamType {
        return when {
            secondIsFinished() -> {
                HLog.d(HTag.TAG, "二流成功")
                AiStreamType.Second
            }

            secondCorrectIsFinished() -> {
                HLog.d(HTag.TAG, "二次纠错成功")
                AiStreamType.SecondCorrection
            }

            secondCorrectStructLoading() -> {
                HLog.d(HTag.TAG, "二次纠错结果加载中")
                AiStreamType.SecondCorrection
            }

            firstCorrectStructLoading() -> {
                HLog.d(HTag.TAG, "一流纠错结果加载中")
                AiStreamType.FirstCorrection
            }

            firstIsFinished() -> {
                HLog.d(HTag.TAG, "一流成功")
                AiStreamType.First
            }

            firstCorrectIsFinished() -> {
                HLog.d(HTag.TAG, "一流纠错成功")
                AiStreamType.FirstCorrection
            }

            titleIsFinished() -> {
                HLog.d(HTag.TAG, "审题立意成功")
                AiStreamType.Title
            }

            else -> {
                AiStreamType.Default
            }
        }
    }

    fun getFailedType(): AiStreamType {
        return when {
            titleIsFailed() -> {
                HLog.d(HTag.TAG, "审题立意失败")
                AiStreamType.Title
            }

            firstCorrectIsFailed() -> {
                HLog.d(HTag.TAG, "一流纠错失败")
                AiStreamType.FirstCorrection
            }

            firstIsFailed() -> {
                HLog.d(HTag.TAG, "一流失败")
                AiStreamType.First
            }

            secondCorrectIsFailed() -> {
                HLog.d(HTag.TAG, "二次纠错失败")
                AiStreamType.SecondCorrection
            }

            secondIsFailed() -> {
                HLog.d(HTag.TAG, "二流失败")
                AiStreamType.Second
            }

            else -> {
                AiStreamType.Default
            }
        }
    }
}

data class OcrResult(
    var message: String? = null,
    var status: String? = null,
    var createTime: String? = null,

    ) {
    fun getOcrJobStatus(): JobStatus {
        return JobStatus.getEnumByValue(status)
    }
}
