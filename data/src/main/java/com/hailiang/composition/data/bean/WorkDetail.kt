package com.hailiang.composition.data.bean


/**
 * Description: 作文信息，包括教师批改信息
 *
 * <AUTHOR>
 * @version 2025/2/25 20:20
 */
data class WorkDetail(
    /**
     * 作业信息
     */
    val schoolworkInfo: SchoolWorkInfo?,

    val schoolworkStateInfo: SchoolWorkStateInfo?,
    /**
     * 学生答案信息
     */
    val studentAnswerInfo: StudentAnswerInfo?,
    /**
     * 议论文材料
     */
    val taskList: List<TaskBean>?,
    val imgInfoList: List<ImageAnswerBean>?,
    val studentCheckInfo: CompositionCheckBean?,
    /**
     * AI教师 二稿批改
     */
    val teacherCheckInfo: CompositionCheckBean?,
    val feedbackInfoList: List<FeedbackInfoBean>?,
) {
    companion object {
        fun empty(): WorkDetail {
            return WorkDetail(
                null, null, null, null, null, null, null, null
            )
        }
    }

    /**
     *
     */
    data class FeedbackInfoBean(
//        val id: Long,
        val schoolworkId: Long,
        val feedbackUserId: Long,
        val feedbackType: String?,
        val articleType: String?,
        val userType: String?,
        val evaluateType: String?,
    )
}