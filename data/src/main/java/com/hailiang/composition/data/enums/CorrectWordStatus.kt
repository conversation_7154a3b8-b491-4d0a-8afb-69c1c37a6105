package com.hailiang.composition.data.enums


class CorrectWordStatus {

}

enum class CorrectWordWay(val value: String) {
    ADD("add"), DELETE("delete"), UPDATE("update");

    companion object {
        fun getEnumByValue(value: String?): CorrectWordWay? {
            if (value.isNullOrEmpty()) return null
            for (status in CorrectWordWay.entries) {
                if (status.value == value) {
                    return status
                }
            }
            return null
        }
    }
}

enum class CorrectWordType(val value: String) {
    MINOR("minor"), SERIOUS("serious");

    companion object {
        fun getEnumByValue(value: String?): CorrectWordType? {
            if (value.isNullOrEmpty()) return null
            for (status in CorrectWordType.entries) {
                if (status.value == value) {
                    return status
                }
            }
            return null
        }
    }
}