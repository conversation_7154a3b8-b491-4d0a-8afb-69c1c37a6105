package com.hailiang.composition.data

import com.alibaba.fastjson2.JSONObject
import com.hailiang.composition.data.bean.AiGuidance
import com.hailiang.composition.data.bean.CompositionCheckBean
import com.hailiang.composition.data.bean.CompositionStatusResponse
import com.hailiang.composition.data.bean.WorkDetail
import com.hailiang.composition.data.bean.response.FirstSubmitResponse
import com.hailiang.libhttp.BaseHttpResult
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.POST

interface ApiService {
    @POST("tch-schoolwork/v1/self_study_room/composition_add")
    suspend fun addComposition(@Body body: RequestBody): BaseHttpResult<FirstSubmitResponse>


    @POST("/tch-schoolwork/v1/self_study_room/en/ocr_second/get_row")
    suspend fun ocrSecond(@Body body: RequestBody): BaseHttpResult<Any>

    // 获取我的作业列表
    @POST("/tch-schoolwork/v1/self_study_room/composition_get_list")
    suspend fun getCompositionList(@Body body: Any): BaseHttpResult<CompositionListWrapper>

    /**
     * 获取作文作业详情
     */
    @POST("/tch-schoolwork/v1/schoolwork/student/composition/composition_info_get_row")
    suspend fun requestCompositionInfo(@Body body: Any): BaseHttpResult<WorkDetail>

    /**
     * 获取学生作业AI批改详情
     */
    @POST("/tch-schoolwork/v1/ai_model/article/student/ai_judge/get_row")
    suspend fun requestStudentAiCorrectInfo(@Body body: Any): BaseHttpResult<CompositionCheckBean>


    @POST("tch-schoolwork/v1/schoolwork/student/composition/hand_writing/judge_job_add")
    suspend fun requestJudgeJobAdd(@Body body: Any): BaseHttpResult<Any>

    /**
     * 二次作答提交
     */
    @POST("/tch-schoolwork/v1/student/schoolwork/composition/answer_add")
    suspend fun studentCompositionSecondSubmit(@Body body: Any): BaseHttpResult<Any>

    /**
     * 点赞
     */
    @POST("/tch-schoolwork/v1/schoolwork/ai_feedback/feedback_evaluate_add")
    suspend fun addFeedbackEvaluate(@Body body: Any): BaseHttpResult<Any>

    /**
     * 取消赞
     */
    @POST("/tch-schoolwork/v1/schoolwork/ai_feedback/feedback_evaluate_del")
    suspend fun cancelFeedbackEvaluate(@Body body: Any): BaseHttpResult<Any>


    /**
     * 更新查看作业的状态
     */
    @POST("schoolwork/studentSchoolwork/updateStudentSchoolWork")
    suspend fun updateWatchStatus(@Body body: Any): BaseHttpResult<Any>

    /**
     * 学生重试：AI一次批改
     */
    @POST("/tch-schoolwork/v1/ai_model/article/student/ai_first_judge/retry/edit")
    suspend fun retryAiFirstJudge(@Body body: Any): BaseHttpResult<Any>

    /**
     * 学生重试：AI二次批改
     */
    @POST("/tch-schoolwork/v1/ai_model/article/student/ai_second_judge/retry/edit")
    suspend fun retryAiSecondJudge(@Body body: Any): BaseHttpResult<Any>

    /**
     * 获取AI批改流式输出状态
     */
    @POST("/tch-schoolwork/v1/self_study_room/en/stream/status_get_row")
    suspend fun checkAiStreamStatus(@Body body: RequestBody): BaseHttpResult<CompositionStatusResponse>

    @POST("/tch-schoolwork/v1/beginner_guide/student/add")
    suspend fun addBeginnerGuidance(@Body body: Any): BaseHttpResult<Any>

    @POST("/tch-schoolwork/v1/beginner_guide/student/get_row")
    suspend fun getBeginnerGuidance(@Body body: Any): BaseHttpResult<AiGuidance>

    @POST("/tch-schoolwork/v1/self_study_room/composition_del")
    suspend fun deleteComposition(@Body body: Any): BaseHttpResult<Any>

    @POST("/tch-schoolwork/v1/self_study_room/text_correction/model_get_row")
    suspend fun getTextCorrectionInfo(): BaseHttpResult<JSONObject>
    /**
     * 作文打分重试
     */
    @POST("/tch-schoolwork/v1/self_study_room/en/score/retry")
    suspend fun retryMakeScore(@Body body: RequestBody): BaseHttpResult<Any>

}