package com.hailiang.composition.data

import com.hailiang.composition.data.bean.AiStreamDetail
import com.hailiang.composition.data.bean.AiStreamReason
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.libhttp.ok.OkHttpHelper
import com.hailiang.libhttp.request.HlRequest
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.launch
import okhttp3.OkHttpClient
import okhttp3.Response
import okhttp3.sse.EventSource
import okhttp3.sse.EventSourceListener
import okhttp3.sse.EventSources
import java.net.SocketException
import java.net.SocketTimeoutException
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

class AiStreamClient<T : AiStreamClient.StreamChunk>(
    private val dataParser: (String?) -> T?,
) {
    private val connectionLock = ReentrantLock()
    private val activeFlows = ConcurrentHashMap<String, SharedFlow<AiStreamDetail>>()
    private val activeEventSources = ConcurrentHashMap<String, EventSource>()
    private val timeout = 60L * 4   // 3分钟
    private val client =
        OkHttpClient.Builder().connectTimeout(timeout, TimeUnit.SECONDS).readTimeout(timeout, TimeUnit.SECONDS)
            .writeTimeout(timeout, TimeUnit.SECONDS).build()

    fun startSSE(
        connectionId: String,
        request: HlRequest,
    ): SharedFlow<AiStreamDetail> {
        connectionLock.withLock {
            // 检查是否已存在相同的流
            activeFlows[connectionId]?.let { return it }
            val flow = MutableSharedFlow<AiStreamDetail>(replay = 1)
            request.header
//                .header("Accept", "text/event-stream")
                .header("Accept", "*/*").header("Content-Type", "application/json;")
            request.log()
            val eventSource = createEventSource(request = request, flow = flow, connectionId = connectionId)
            activeEventSources[connectionId] = eventSource
            activeFlows[connectionId] = flow
            return flow
        }
    }

    private fun createEventSource(
        request: HlRequest,
        flow: MutableSharedFlow<AiStreamDetail>,
        connectionId: String,
    ): EventSource {
        val reasonBuilder = StringBuilder()
        val answerBuilder = StringBuilder()
        var isClosed = false
        val eventSourceListener = object : EventSourceListener() {
            override fun onOpen(eventSource: EventSource, response: Response) {
                HLog.i(HTag.TAG, "SSE 连接已打开")
            }

            override fun onEvent(
                eventSource: EventSource,
                id: String?,
                type: String?,
                data: String,
            ) {
                try {
//                    HLog.i(
//                        HTag.TAG,
//                        "SSE onEvent(Thread: ${Thread.currentThread().id}) id: $id; type: $type; data: $data"
//                    )
                    val chunk = dataParser(data)
                    chunk?.reasonContent()?.let {
                        reasonBuilder.append(it)
                    }
                    chunk?.answerContent()?.let {
                        answerBuilder.append(it)
                    }
                    // 检查是否是最后一条消息
                    if (isCompletionMessage(chunk)) {
                        HLog.i(HTag.TAG, "是结束消息，流输出完成")
                        cleanupFlow(connectionId)
                        isClosed = true
                        activeEventSources[connectionId]?.cancel()
                        activeEventSources.remove(connectionId)
                    }
                    receive(
                        flow = flow,
                        reasonBuilder = reasonBuilder,
                        answerBuilder = answerBuilder,
                        finishReason = chunk?.finishReason()
                    )
                } catch (e: Exception) {
                    HLog.w(HTag.TAG, "数据解析失败: ${e.message}")
                }
            }

            override fun onClosed(eventSource: EventSource) {
                // 只有在异常关闭时才清理 Flow
                isClosed = true
                HLog.i(HTag.TAG, "SSE 连接已关闭")
                if (activeEventSources.containsKey(connectionId)) {
                    HLog.i(HTag.TAG, "未收到结束消息，但是SSE连接已关闭，清理流，下次重试")
                    cleanupFlow(connectionId)
                    failure(
                        flow = flow,
                        reasonBuilder = reasonBuilder,
                        answerBuilder = answerBuilder,
                        finishReason = AiStreamReason.Error.value
                    )
                }
            }

            override fun onFailure(
                eventSource: EventSource,
                t: Throwable?,
                response: Response?,
            ) {
                cleanupFlow(connectionId)
                HLog.w(HTag.TAG, "SSE 连接失败($connectionId) response: ${response?.message}(${response?.code}); ", t)
                when (t) {
                    is SocketTimeoutException, is IllegalStateException -> {
                        failure(
                            flow = flow,
                            reasonBuilder = reasonBuilder,
                            answerBuilder = answerBuilder,
                            finishReason = AiStreamReason.NetworkError.value
                        )
                    }

                    is SocketException -> {
                        if (isClosed && response?.isSuccessful == true) {
                            HLog.w(
                                HTag.TAG,
                                "SSE onFailure; response: ${response.message}(${response.code}); 流正常关闭",
                                t
                            )
                            return
                        }
                        failure(
                            flow = flow,
                            reasonBuilder = reasonBuilder,
                            answerBuilder = answerBuilder,
                            finishReason = AiStreamReason.NetworkError.value
                        )
                    }

                    else -> {
                        if (!isClosed) {
                            failure(
                                flow = flow,
                                reasonBuilder = reasonBuilder,
                                answerBuilder = answerBuilder,
                                finishReason = AiStreamReason.Error.value
                            )
                        } else {
                            HLog.d(HTag.TAG, "SSE 连接已关闭，无需处理失败")
                        }
                    }
                }

            }
        }
        return EventSources.createFactory(client)
            .newEventSource(OkHttpHelper.buildRequest(request), eventSourceListener)
    }

    // 判断是否是完成消息的方法
    private fun isCompletionMessage(chunk: StreamChunk?): Boolean {
        if (chunk == null) {
            return false
        }
        return chunk.isCompletionMessage()
    }

    private fun receive(
        flow: MutableSharedFlow<AiStreamDetail>,
        reasonBuilder: StringBuilder,
        answerBuilder: StringBuilder,
        finishReason: String?,
    ) {
        MainScope().launch(Dispatchers.Default) {
            flow.emit(
                AiStreamDetail(
                    reasoning = reasonBuilder.toString(),
                    answering = answerBuilder.toString(),
                    finishReason = finishReason,
                )
            )
        }
    }

    private fun failure(
        flow: MutableSharedFlow<AiStreamDetail>,
        reasonBuilder: StringBuilder,
        answerBuilder: StringBuilder,
        finishReason: String?,
    ) {
        MainScope().launch(Dispatchers.Default) {
            flow.emit(
                AiStreamDetail(
                    reasoning = reasonBuilder.toString(),
                    answering = answerBuilder.toString(),
                    finishReason = finishReason
                )
            )
        }
    }

    // ----------------------------------------------------------------------
    private fun cleanupFlow(connectionId: String) {
        connectionLock.withLock {
            // 不关闭，否则会导致服务端也关闭流。
            activeEventSources.remove(connectionId)?.cancel()
            activeFlows.remove(connectionId)
        }
    }

    fun closeFlow(connectionId: String) {
        cleanupFlow(connectionId)
    }

    fun closeAllFlows() {
        connectionLock.withLock {
            activeEventSources.keys.toList().forEach { cleanupFlow(it) }
        }
    }

    interface StreamChunk {
        /**
         * 获取推理思考内容
         */
        fun reasonContent(): String?
        fun answerContent(): String?
        fun finishReason(): String?

        //
        fun isCompletionMessage(): Boolean
        fun isError(): Boolean
    }
//
//    interface ClientCallback {
//        fun onEvent(aiStreamDetail: AiStreamDetail)
//    }
}