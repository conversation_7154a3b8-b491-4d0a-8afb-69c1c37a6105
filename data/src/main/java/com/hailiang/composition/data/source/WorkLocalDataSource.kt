package com.hailiang.composition.data.source

import com.hailiang.composition.data.bean.WorkStatus
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.workcloud.database.room.LocalDatabase
import com.hailiang.workcloud.database.room.dao.DoWorkStateDao
import com.hailiang.workcloud.database.room.pojo.DoWorkStatePo

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/5/23 17:34
 */
class WorkLocalDataSource {
    private val doWorkStateDao: DoWorkStateDao by lazy {
        LocalDatabase.database.getDoWorkStateDao()
    }

    fun queryWorkState(workId: Long, workStateId: Long): DoWorkStatePo? {
        return doWorkStateDao.getByWorkIdAndStateId(workId = workId, workStateId = workStateId)
    }

    fun findOrCreateWorkState(workId: Long, workStateId: Long): DoWorkStatePo {
        return doWorkStateDao.getByWorkIdAndStateId(workId = workId, workStateId = workStateId)
            ?: DoWorkStatePo(
                id = null,
                workId = workId,
                workStateId = workStateId,
                startTime = System.currentTimeMillis(),
                submitTime = 0,
                timeCounting = 0,
                state = WorkStatus.UN_DO,
                studentCheckScore = 0f,
                errorMessage = null,
            )
    }

    fun updateWorkState(doWorkState: DoWorkStatePo) {
        val id = if (doWorkState.id == null) {
            queryWorkState(workId = doWorkState.workId, workStateId = doWorkState.workStateId)?.id
        } else {
            doWorkState.id
        }
        HLog.i(HTag.TAG, "updateWorkState doWorkState(id:${id}); $doWorkState")
        doWorkStateDao.insertOrUpdate(doWorkState.copy(id = id))
    }

    fun updateWorkState(workId: Long, workStateId: Long, newState: Int) {
        updateWorkState(
            findOrCreateWorkState(workId = workId, workStateId = workStateId).copy(
                state = newState
            )
        )
    }

    fun clearWorkErrorMessage(workId: Long, workStateId: Long) {
        // 没有状态时，不更新
        queryWorkState(workId = workId, workStateId = workStateId)?.let {
            updateWorkState(it.copy(errorMessage = null))
        }
    }
}