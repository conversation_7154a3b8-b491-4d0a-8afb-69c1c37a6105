package com.hailiang.composition.data.bean.request

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/2/27 15:27
 */
data class CompositionSecondSubmitRequest(
    val schoolworkId: Long,             // 作业id
    val schoolworkStateId: Long,        // 学生作业id
    val startTime: String,                // 开始答题时间
    val submitTime: String,               // 结束答题时间
    val time: Long,                     // 做题耗时
    val status: Int,                      // 状态
    val studentCheckScore: Float,        // 学生自批成绩
    val answerList: List<AnswerDetail>,          // 答题详情
) {

    // 答题详情
    data class AnswerDetail(
        val studentFirstAnswerDetail: String,    // 学生第一次答题详情(ocr结果)
        val studentSecondAnswerDetail: String,   // 学生第二次答题详情(改写过后)
        val studentEditDetail: String,            // 学生编辑详情(记录编辑的内容)
    )
}