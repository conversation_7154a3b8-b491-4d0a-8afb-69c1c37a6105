package com.hailiang.composition.data.enums

// 状态枚举
enum class JobStatus(val value: String) {
    NONE("none"), INIT("init"), RUNNING("running"), SUCCESS("success"), FAILED("failed");

    companion object {
        fun getEnumByValue(value: String?): JobStatus {
            if (value.isNullOrEmpty()) return NONE
            for (status in JobStatus.entries) {
                if (status.value == value) {
                    return status
                }
            }
            return FAILED
        }
    }

//    fun preparing(): Boolean {
//        return this == NONE || this == INIT
//    }

    fun isNone(): Boolean {
        return this == NONE
    }

    fun isRunning(): Boolean {
        return this == NONE || this == INIT || this == RUNNING
    }

    fun isSuccess(): Boolean {
        return this == SUCCESS
    }

    fun isFailed(): <PERSON><PERSON><PERSON> {
        return this == FAILED
    }
}