package com.hailiang.composition.data.bean

/**
 * 作业状态
 */
data class SchoolWorkStateInfo(
    val id: Long,                          // 作业状态id
    val startTime: String?,                // 开始答题时间
    val submitTime: String?,               // 提交时间
    val status: Int,                      // 答题状态
    val userId: Long,                      // 用户id
    val studentCheckScore: Float,        // 学生自批成绩
    val teacherFirstCheckScore: Float,   // 老师首次批改成绩
    val score: Float,                    // 老师批改最终成绩
    val teacherCheckFlag: Int,            // 教师是否批改
    val teacherCheckTime: String?,         // 老师批改时间
    val selfViewFlag: Int,              // 学生是否已查看, 1:已查看, 0:未查看
    val time: Long,                         // 时长, ms
)