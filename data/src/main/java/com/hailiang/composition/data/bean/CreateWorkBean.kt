package com.hailiang.composition.data.bean

import com.hailiang.opensdk.LauncherData

data class CreateWorkBean(
    var taskList: List<TaskBean>,
    var compositionImageList: List<String>,
    var subject: Int,
    var grade: Long,
    var educationStage: String,
    var classIdList: List<Long>,
    var classNameList: List<String>,
    var schoolworkStateId: Long? = null,
    var schoolworkId: Long? = null
) {
    constructor() : this(
        taskList = emptyList(),
        compositionImageList = emptyList(),
        subject = 75,
        grade = LauncherData.getUserInfo()?.gradeId?.toLong() ?: 0,
        educationStage = "",
        classIdList = LauncherData.getUserInfo()?.classInfos?.map { it.classId.toLong() }?: emptyList(),
        classNameList = LauncherData.getUserInfo()?.classInfos?.map { it.className }?: emptyList()
    )
}
