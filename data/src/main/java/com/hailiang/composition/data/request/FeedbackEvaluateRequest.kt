package com.hailiang.composition.data.bean.request

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/3/19 16:32
 */
sealed class FeedbackEvaluateRequest {
    data class Add(
        val schoolworkId: Long,
        val schoolworkStateId: Long,
        /**
         * 反馈类型 overallMerit|综合评价 giveAdvice|点拨 knowledgeGasStation|知识加油站
         */
        val feedbackType: String,
        /**
         * 文章类型 firstDraft|初稿 secondDraft|二稿
         */
        val articleType: String,
        /**
         * 类型 teacher|老师 student|学生
         */
        val userType: String,
        /**
         * 评价类型 like|点赞 dislike|点踩
         */
        val evaluateType: String,
    ) : FeedbackEvaluateRequest()

    data class Delete(
        val schoolworkId: Long,
        val schoolworkStateId: Long,
        /**
         * 反馈类型 overallMerit|综合评价 giveAdvice|点拨 knowledgeGasStation|知识加油站
         */
        val feedbackType: String,
        /**
         * 文章类型 firstDraft|初稿 secondDraft|二稿
         */
        val articleType: String,
        /**
         * 类型 teacher|老师 student|学生
         */
        val userType: String,
    ) : FeedbackEvaluateRequest()
}

sealed class FeedbackType(val value: String) {
    /**
     * overallMerit|综合评价
     */
    object OverallMerit : FeedbackType("overallMerit")

    /**
     *  giveAdvice|点拨
     */
    object GiveAdvice : FeedbackType("giveAdvice")

    /**
     * knowledgeGasStation|知识加油站
     */
    object KnowledgeGasStation : FeedbackType("knowledgeGasStation")

    /**
     * 一稿
     */
    object OnePage : FeedbackType("onePage")
}

sealed class EvaluateType(val value: String) {
    object Like : EvaluateType("like")
    object Dislike : EvaluateType("dislike")
}

sealed class ArticleType(val value: String) {
    object FirstDraft : ArticleType("firstDraft")
    object SecondDraft : ArticleType("secondDraft")
}