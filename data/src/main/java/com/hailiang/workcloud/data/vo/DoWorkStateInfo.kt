package com.hailiang.workcloud.data.vo

import com.hailiang.common.util.DateUtil
import com.hailiang.composition.data.bean.WorkDetail
import com.hailiang.composition.data.bean.WorkStatus

/**
 * 做题状态
 */
data class DoWorkStateInfo(
    /**
     * 作业id：服务端
     */
    val workId: Long,
    /**
     * 作业状态id：服务端
     */
    val workStateId: Long,
    var startTime: Long,
    var submitTime: Long,
    /**
     * 计时，做题耗时，毫秒
     */
    var timeCounting: Long,
    /**
     * 作业作业状态：WorkStatus
     */
    var state: Int,
    var studentCheckScore: Float,
    /**
     * 最近作业的一些异常状态，OCR 识别失败，AI 批改失败等等，方便后续回显，
     */
    var errorMessage: String?,
) {
    companion object {
        fun empty(
            workId: Long = 0,
            workStateId: Long = 0,
            state: Int = WorkStatus.UN_DO,
        ): DoWorkStateInfo {
            return DoWorkStateInfo(
                workId = workId,
                workStateId = workStateId,
                startTime = System.currentTimeMillis(),
                submitTime = 0,
                timeCounting = 0,
                state = state,
                studentCheckScore = 0f,
                errorMessage = null,
            )
        }

        fun obtainByWorkDetail(workDetail: WorkDetail): DoWorkStateInfo {
            return DoWorkStateInfo(
                workId = workDetail.schoolworkInfo?.id ?: -1,
                workStateId = workDetail.schoolworkStateInfo?.id ?: -1,
                startTime = DateUtil.stringToTimeMillis(
                    workDetail.schoolworkStateInfo?.startTime,
                    System.currentTimeMillis()
                ),
                submitTime = DateUtil.stringToTimeMillis(
                    workDetail.schoolworkStateInfo?.submitTime,
                    0
                ),
                timeCounting = workDetail.schoolworkStateInfo?.time ?: 0,
                state = workDetail.schoolworkStateInfo?.status ?: WorkStatus.UN_DO,
                studentCheckScore = workDetail.schoolworkStateInfo?.studentCheckScore ?: 0f,
                errorMessage = null,
            )
        }
    }
}