package com.hailiang.workcloud.data.vo

import com.hailiang.composition.data.bean.CompositionCheckBean.Advice
import com.hailiang.composition.data.bean.CompositionCheckBean.Allusion
import com.hailiang.composition.data.bean.CompositionCheckBean.ComprehensiveJudge
import com.hailiang.composition.data.bean.CompositionCheckBean.CorrectWord
import com.hailiang.composition.data.enums.JobStatus


/**
 * Description: 本地 议论文作答记录表
 *
 * <AUTHOR>
 * @version 2025/2/27 19:12
 */
data class CompositionCheckDetail(

    /**
     * 初始化 init 进行中 running 成功 success 失败 failed
     */
    val jobStatus: String,

    /**
     * 额外信息
     */
    val message: String?,

    /**
     * 作业id：服务端
     */
    val workId: Long,

    /**
     * 作业状态id：服务端
     */
    val workStateId: Long,

    /**
     * OCR识别结果
     */
    val ocrTitle: String?,
    /**
     * OCR识别结果
     */
    val ocrContent: String?,

    /**
     * 综合评价
     */
    val comprehensiveJudge: ComprehensiveJudge?,
    /**
     * 点拨
     */
    val adviceList: List<Advice>?,

    /**
     * 典故列表（知识加油站）
     */
    val allusionList: List<Allusion>?,


    /**
     * 纠错内容
     */
    val correctWords: List<CorrectWord>?,
    /**
     * 分数
     */
    val score: Float,
    val totalScore: Int,
) {
    var scoreJobStatus: JobStatus = JobStatus.getEnumByValue(jobStatus)
    var allusionJobStatus: JobStatus = JobStatus.getEnumByValue(jobStatus)


    fun isDirty(): Boolean {
        return jobStatus != JobStatus.SUCCESS.value || !ocrContent.isNullOrEmpty()
    }
}