plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
}

val APP_VERSION_NAME: String by rootProject.extra
val APP_VERSION_CODE: String by rootProject.extra

android {
    namespace = "com.hailiang.xxb.en.composition"
    compileSdk = libs.versions.compileSdk.get().toInt()

    defaultConfig {
        applicationId = "com.hailiang.xxb.en.composition"
        minSdk = libs.versions.minSdk.get().toInt()
        targetSdk = libs.versions.targetSdk.get().toInt()
        versionCode = APP_VERSION_CODE.toInt()
        versionName = APP_VERSION_NAME

        ndk{
            abiFilters.add("arm64-v8a")
        }

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }
    val defaultDimension = "default"
    flavorDimensions += defaultDimension
    productFlavors {
        create("otest") {
            dimension = defaultDimension
            resValue("string", "app_name", "英语作文批改(测试)")
            buildConfigField("String", "BuglyAppID", "\"93014c6479\"")
            buildConfigField("String", "BASE_URL", "\"http://jzjx-api-test.hailiangjy.com/\"")
        }
        create("dev") {
            dimension = defaultDimension
            resValue("string", "app_name", "英语作文批改(开发)")
            buildConfigField("String", "BuglyAppID", "\"93014c6479\"")
            buildConfigField("String", "BASE_URL", "\"http://jzjx-api-dev.hailiangjy.com/\"")
        }
        create("prod") {
            dimension = defaultDimension
            resValue("string", "app_name", "英语作文批改")
            buildConfigField("String", "BuglyAppID", "\"16991ba02a\"")
            buildConfigField("String", "BASE_URL", "\"https://jzjx-api.hailiangedu.com/\"")
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
    buildFeatures {
        viewBinding = true
        buildConfig = true
        compose = true
    }
    composeOptions {
        kotlinCompilerExtensionVersion = libs.versions.compose.compiler.get()
    }
    configurations.all {
        resolutionStrategy {
            cacheChangingModulesFor(0, TimeUnit.SECONDS)
            exclude(group = "com.hailiang.hlutil", module = "hlutil")
            exclude(group = "com.hailiang.handwrite", module = "handwrite")
            exclude(group = "com.hailiang.core", module = "libhttp")
        }
    }
}

dependencies {
    implementation(project(":common"))
    implementation(project(":data"))
    implementation(project(":database"))
    implementation("com.hailiang.core:question-ui:1.0.7.1")
    implementation("com.hailiang.hlobs:hlobs:1.1.1")
    implementation("com.tencent.tav:libpag:4.3.68")

    implementation(libs.hailiang.camera)
    implementation(libs.camera.core)
    implementation(libs.camera.camera2)
    implementation(libs.camera.lifecycle)
    implementation(libs.camera.extensions)
    implementation(libs.camera.view)

    implementation(libs.hailiang.markdown)
    implementation(libs.hailiang.question.composition)
    implementation(libs.hailiang.question.en.composition)
    implementation(libs.hailiang.handwrite)
    implementation(libs.hailiang.handwrite.toolbar)
    implementation(libs.hailiang.symspellcorrect)
//    implementation(project(":symspellcorrect"))
    implementation("io.coil-kt:coil-compose:2.6.0")
    implementation("io.coil-kt:coil-gif:2.6.0")
//    implementation(project(":textcorrection"))
}

apply(from = "${project.rootDir}/common/config.gradle")