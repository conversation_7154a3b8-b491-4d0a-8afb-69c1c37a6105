<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <application
        android:name="com.hailiang.common.base.XxbApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher2"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true"
        android:theme="@style/Theme.Composition">
        <activity
            android:name="com.hailiang.composition.ui.TestActivity"
            android:exported="true">

        </activity>
        <activity
            android:name="com.hailiang.composition.ui.main.MainActivity"
            android:exported="true"
            android:screenOrientation="landscape">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hailiang.composition.ui.guidance.TakePhotoGuidanceActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="landscape" />
        <activity
            android:name="com.hailiang.composition.ui.practice.CompositionGuidanceActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|fontScale|uiMode"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="adjustResize|stateHidden" />

        <activity
            android:name="com.hailiang.composition.ui.photograph.TakePhotoCompositionActivity"
            android:screenOrientation="landscape" />
    </application>

</manifest>