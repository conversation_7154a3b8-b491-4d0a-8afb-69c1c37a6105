package com.hailiang.composition.mediatools;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.MultiTransformation;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestOptions;
import com.hailiang.camera.pictureselector.entity.HLLocalMedia;
import com.hailiang.xxb.en.composition.R;
import com.luck.picture.lib.adapter.holder.PreviewGalleryAdapter;

import java.util.ArrayList;

import static com.hailiang.camera.ucrop.util.DensityUtil.dip2px;


public class GridImageAdapter extends RecyclerView.Adapter<GridImageAdapter.ViewHolder> {
    public static final String TAG = "PictureSelector";
    public static final int TYPE_CAMERA = 1;
    public static final int TYPE_PICTURE = 2;
    private final LayoutInflater mInflater;
    public ArrayList<MediaImage> list;
    private int selectMax = 9;


    public GridImageAdapter(Context context) {
        this.mInflater = LayoutInflater.from(context);
    }

    public void setSelectMax(int selectMax) {
        this.selectMax = selectMax;
    }

    public int getSelectMax() {
        return selectMax;
    }

    public ArrayList<MediaImage> getData() {
        return list;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {

        ImageView mImg;
        ImageView mIvDel;
        ImageView mIvUploading;
        TextView tvUploading;
        ImageView ivUploadFailed;
        TextView tvUploadFailed;
        TextView btnRetry;
        View vMask;

        public ViewHolder(View view) {
            super(view);
            mImg = view.findViewById(R.id.iv_photo);
            mIvDel = view.findViewById(R.id.btn_delete);
            mIvUploading = view.findViewById(R.id.ivUploading);
            tvUploading = view.findViewById(R.id.tvUploading);
            ivUploadFailed = view.findViewById(R.id.ivUploadFailed);
            tvUploadFailed = view.findViewById(R.id.tvUploadFailed);
            btnRetry = view.findViewById(R.id.btnRetry);
            vMask = view.findViewById(R.id.vMask);
        }
    }

    @Override
    public int getItemCount() {
        if (list.size() < selectMax) {
            return list.size() + 1;
        } else {
            return list.size();
        }
    }

    @Override
    public int getItemViewType(int position) {
        if (isShowAddItem(position)) {
            return TYPE_CAMERA;
        } else {
            return TYPE_PICTURE;
        }
    }

    /**
     * 创建ViewHolder
     */
    @Override
    public ViewHolder onCreateViewHolder(ViewGroup viewGroup, int i) {
        View view = mInflater.inflate(R.layout.item_composition_photo, viewGroup, false);
        return new ViewHolder(view);
    }

    private boolean isShowAddItem(int position) {
        int size = list.size();
        return position == size;
    }

    public ArrayList<HLLocalMedia> getLocalMediaList() {
        ArrayList<HLLocalMedia> lMList = new ArrayList<>();
        for (MediaImage mediaImage : list) {
            HLLocalMedia media = mediaImage.getMedia();
            lMList.add(media);
        }
        return lMList;
    }

    @Override
    public void onViewDetachedFromWindow(@NonNull ViewHolder holder) {
        super.onViewDetachedFromWindow(holder);

    }

    /**
     * 设置值
     */
    @Override
    public void onBindViewHolder(final ViewHolder viewHolder, final int position) {
        //少于MaxSize张，显示继续添加的图标
        if (getItemViewType(position) == TYPE_CAMERA) {
            viewHolder.mImg.setOnClickListener(view -> {
                if (mItemClickListener != null) {
                    mItemClickListener.openPicture();
                }
            });
            viewHolder.mIvDel.setVisibility(View.INVISIBLE);
        } else {
            viewHolder.mIvDel.setVisibility(View.VISIBLE);
            viewHolder.mIvDel.setOnClickListener(view -> {
                int index = viewHolder.getAbsoluteAdapterPosition();
                if (index != RecyclerView.NO_POSITION && list.size() > index) {
                    list.remove(index);
                    notifyItemRemoved(index);
                    notifyItemRangeChanged(index, list.size());
                }
                if (mItemClickListener != null) {
                    mItemClickListener.delete(index);
                }
            });
//            viewHolder.itemView.setClipToOutline(false);

            HLLocalMedia media = list.get(position).getMedia();
            String path = media.getAvailablePath();
            RequestOptions requestOptions = new RequestOptions()
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .transform(new MultiTransformation<>(
                            new CenterCrop(),  // 先做 centerCrop
                            new RoundedCorners(dip2px(viewHolder.itemView.getContext(), 7))  // 再加圆角
                    ));            //itemView 的点击事件

            Glide.with(viewHolder.itemView.getContext())
                    .load(path)
                    .apply(requestOptions)
                    .into(viewHolder.mImg);

            if (mItemClickListener != null) {
                viewHolder.itemView.setOnClickListener(v -> {
                    int adapterPosition = viewHolder.getAbsoluteAdapterPosition();
                    mItemClickListener.onItemClick(v, adapterPosition);
                });
            }

            if (mItemLongClickListener != null) {
                viewHolder.itemView.setOnLongClickListener(v -> {
                    int adapterPosition = viewHolder.getAbsoluteAdapterPosition();
                    mItemLongClickListener.onItemLongClick(viewHolder, adapterPosition, v);
                    return true;
                });
            }
            if (list.get(position).getState() == MediaImage.STATE_DEFAULT || list.get(position).getState() == MediaImage.STATE_SUCCESS) {
                viewHolder.mIvDel.setVisibility(View.VISIBLE);
                viewHolder.mIvUploading.setVisibility(View.GONE);
                viewHolder.ivUploadFailed.setVisibility(View.GONE);
                viewHolder.tvUploading.setVisibility(View.GONE);
                viewHolder.tvUploadFailed.setVisibility(View.GONE);
                viewHolder.btnRetry.setVisibility(View.GONE);
                viewHolder.vMask.setVisibility(View.GONE);
            } else if (list.get(position).getState() == MediaImage.STATE_LOADING) {
                viewHolder.mIvDel.setVisibility(View.GONE);
                viewHolder.mIvUploading.setVisibility(View.VISIBLE);
                viewHolder.ivUploadFailed.setVisibility(View.GONE);
                viewHolder.tvUploading.setVisibility(View.VISIBLE);
                viewHolder.tvUploadFailed.setVisibility(View.GONE);
                viewHolder.btnRetry.setVisibility(View.GONE);
                viewHolder.vMask.setVisibility(View.VISIBLE);
//                RequestOptions requestOptions2 = new RequestOptions()
//                        .diskCacheStrategy(DiskCacheStrategy.ALL)
//                        .format(DecodeFormat.PREFER_ARGB_8888); // 设置格式为 ARGB_8888

//                Glide.with(viewHolder.itemView.getContext())
////                        .asGif()
//                        .load(R.drawable.icon_upload_loading)
////                        .apply(requestOptions2)
//                        .into(viewHolder.mIvUploading);
            } else if (list.get(position).getState() == MediaImage.STATE_FAILURE) {
                viewHolder.mIvDel.setVisibility(View.VISIBLE);
                viewHolder.mIvUploading.setVisibility(View.GONE);
                viewHolder.ivUploadFailed.setVisibility(View.VISIBLE);
                viewHolder.tvUploading.setVisibility(View.GONE);
                viewHolder.tvUploadFailed.setVisibility(View.VISIBLE);
                viewHolder.btnRetry.setVisibility(View.VISIBLE);
                viewHolder.vMask.setVisibility(View.VISIBLE);
                viewHolder.btnRetry.setOnClickListener(v -> {
                    if (mItemClickListener != null) {
                        mItemClickListener.retry(position);
                    }
                });
            }
        }
    }

    private OnItemClickListener mItemClickListener;

    public void setOnItemClickListener(OnItemClickListener l) {
        this.mItemClickListener = l;
    }

    public interface OnItemClickListener {
        /**
         * Item click event
         *
         * @param v
         * @param position
         */
        void onItemClick(View v, int position);

        /**
         * Open PictureSelector
         */
        void openPicture();

        void retry(int position);

        void delete(int position);
    }

    private PreviewGalleryAdapter.OnItemLongClickListener mItemLongClickListener;

    public void setItemLongClickListener(PreviewGalleryAdapter.OnItemLongClickListener l) {
        this.mItemLongClickListener = l;
    }
}