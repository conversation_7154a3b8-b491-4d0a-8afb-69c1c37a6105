package com.hailiang.composition.log

import com.hailiang.hlutil.HLog
import org.json.JSONObject

/**
 * Description: 业务埋点
 *
 * <AUTHOR>
 * @version 2025/4/2 10:19
 */
object BusinessBury {
    private const val MIN_INTERVAL = 300L

    init {
        HLog.Companion.registerLogCaller(BusinessBury::class.java.name)
    }

    fun studentCompositionDoWorkEvent(
        workId: Long,
        workStateId: Long,
        stepName: String,
        aiTab: String? = null,
        startTimeMillis: Long,
    ) {
        // 默认值 和 时间过短，不埋点
        if (stepName.isEmpty() || (System.currentTimeMillis() - startTimeMillis < MIN_INTERVAL)) return
        val eventInfo = SlsLogHelper.getTimeEventInfo(startTimeMillis, System.currentTimeMillis())
        eventInfo.put("pageStep", stepName)
        eventInfo.put("aiTab", aiTab)
        val businessJson = JSONObject()
        businessJson.put("workId", workId)
        businessJson.put("workStateId", workStateId)
        customBury(
            eventCode = SlsEventCode.STUDENT_COMPOSITION_DO_WORK,
            eventName = "学生AI作文作答",
            eventInfo = eventInfo,
            businessInfo = businessJson
        )
    }

    fun studentCompositionCheckWork(
        workId: Long,
        workStateId: Long,
        stepName: String,
        aiTab: String? = null,
        startTimeMillis: Long,
    ) {
        // 默认值 和 时间过短，不埋点
        if (stepName.isEmpty() || (System.currentTimeMillis() - startTimeMillis < MIN_INTERVAL)) return
        val eventInfo = SlsLogHelper.getTimeEventInfo(startTimeMillis, System.currentTimeMillis())
        eventInfo.put("pageStep", stepName)
        eventInfo.put("aiTab", aiTab)
        val businessJson = JSONObject()
        businessJson.put("workId", workId)
        businessJson.put("workStateId", workStateId)
        customBury(
            eventCode = SlsEventCode.STUDENT_COMPOSITION_LOOK,
            eventName = "学生AI作业查看",
            eventInfo = eventInfo,
            businessInfo = businessJson
        )
    }

    private fun customBury(
        eventCode: String,
        eventName: String,
        eventInfo: JSONObject,
        businessInfo: JSONObject,
    ) {
        SlsLogHelper.customBury(
            eventCode,
            eventName,
            eventInfo,
            businessInfo
        )
    }
}