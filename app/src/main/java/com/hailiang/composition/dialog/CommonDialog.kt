package com.hailiang.composition.dialog

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.DialogFragment
import com.hailiang.hlutil.dp
import com.hailiang.xxb.en.composition.R

class CommonDialog : DialogFragment() {

    private var title: String? = null
    private var contentText: String? = null
    private var image: Int? = null
    private var imageText: String? = null
    private var positiveText: String = "确定"
    private var negativeText: String = "取消"
    private var positiveAction: (() -> Unit)? = null
    private var negativeAction: (() -> Unit)? = null

    private var spanCount = 0
    private var spacingDp = 0

    private var isPositiveButtonVisible = true
    private var isNegativeButtonVisible = true

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val builder = AlertDialog.Builder(requireContext())
        val inflater = requireActivity().layoutInflater
        val view = inflater.inflate(R.layout.dialog_common, null)

        setupTitle(view)
        setupContent(view)
        builder.setView(view)

        val positiveButton = view.findViewById<TextView>(R.id.dialog_confirm)
        val negativeButton = view.findViewById<TextView>(R.id.dialog_cancel)
        negativeButton.text = negativeText
        positiveButton.text = positiveText

        positiveButton.visibility = if (isPositiveButtonVisible) View.VISIBLE else View.GONE
        negativeButton.visibility = if (isNegativeButtonVisible) View.VISIBLE else View.GONE


        negativeButton.setOnClickListener {
            dismiss()
            negativeAction?.invoke()
        }
        positiveButton.setOnClickListener {
            dismiss()
            positiveAction?.invoke()
        }

        val dialog = builder.create()
        dialog.window?.setBackgroundDrawableResource(R.drawable.bg_white_radius20)
        dialog.initHideNavigationBarConfig()
        dialog.setCancelable(true)
        dialog.setCanceledOnTouchOutside(true)
        return dialog
    }

    override fun onStart() {
        super.onStart()
        val window = dialog?.window
        if (window != null) {
            val layoutParams = window.attributes
            layoutParams.width = 500.dp.toInt()
            layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT
            window.attributes = layoutParams
        }

    }

    fun Dialog.initHideNavigationBarConfig() {
        window?.addFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)
        window?.clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)
        hideNavigationBar(this)
    }

    private fun hideNavigationBar(dialog: Dialog) {
        val decorView = dialog.window?.decorView
        decorView?.systemUiVisibility = (View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY)
    }

    private fun setupTitle(view: View) {
        view.findViewById<TextView>(R.id.dialog_title).apply {
            text = title ?: "提示"
            visibility = if (title.isNullOrEmpty()) View.GONE else View.VISIBLE
        }
    }

    private fun setupContent(view: View) {
        val container = view.findViewById<ViewGroup>(R.id.content_container)
        container.removeAllViews()

        // 设置最大高度
        val params = container.layoutParams as ViewGroup.LayoutParams
        params.height = ViewGroup.LayoutParams.WRAP_CONTENT
        container.layoutParams = params

        // 设置最大高度限制
        container.clipToPadding = false
        container.clipChildren = false
        container.setPadding(0, 0, 0, 0)

        val maxHeight = 430.dp
        container.viewTreeObserver.addOnGlobalLayoutListener(object :
            ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                if (container.measuredHeight > maxHeight) {
                    val layoutParams = container.layoutParams
                    layoutParams.height = maxHeight.toInt()
                    container.layoutParams = layoutParams
                    container.viewTreeObserver.removeOnGlobalLayoutListener(this)
                }
            }
        })

        when {
            !contentText.isNullOrEmpty() -> setupTextView(container)
            else -> container.visibility = View.GONE
        }
        if (image != null) {
            setupImageView(container)
            container.visibility = View.VISIBLE
        }
    }

    private fun setupImageView(container: ViewGroup) {
        ImageView(requireContext()).apply {
            setImageResource(image!!)
            layoutParams = ViewGroup.LayoutParams(200.dp.toInt(), 180.dp.toInt())

        }.also {
            container.addView(it)
            if (!imageText.isNullOrEmpty()) {
                TextView(requireContext()).apply {
                    text = imageText
                    typeface = Typeface.DEFAULT_BOLD
                    textSize = 20f
                    setTextColor(Color.parseColor("#926109"))
                    // 设置行间距
                    setLineSpacing(8f, 1.5f)
                    layoutParams = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.WRAP_CONTENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    )
                }.also { textView ->
                    container.addView(textView)
                }
            }
        }
    }
    
    private fun setupTextView(container: ViewGroup) {
        TextView(requireContext()).apply {
            text = contentText
            setTextAppearance(android.R.style.TextAppearance_Medium)
            textSize = 17f
            gravity = Gravity.CENTER_HORIZONTAL
            setTextColor(Color.parseColor("#A6000000"))
            // 设置行间距
            setLineSpacing(8f, 1.5f)
        }.also { container.addView(it) }
    }


    class Builder {
        private val dialog = CommonDialog()

        fun setTitle(title: String): Builder {
            dialog.title = title
            return this
        }

        fun setContentText(text: String): Builder {
            dialog.contentText = text
            return this
        }

        fun setContentImage(image: Int, text: String?): Builder {
            dialog.image = image
            dialog.imageText = text
            return this
        }
        fun setNegativeAction(text: String = "取消", action: () -> Unit): Builder {
            dialog.negativeText = text
            dialog.negativeAction = action
            return this
        }

        fun setPositiveAction(text: String = "确定", action: () -> Unit): Builder {
            dialog.positiveText = text
            dialog.positiveAction = action
            return this
        }

        fun setNegativeText(text: String): Builder {
            dialog.negativeText = text
            return this
        }

        fun setPositiveText(text: String): Builder {
            dialog.positiveText = text
            return this
        }

        fun setPositiveButtonVisible(visible: Boolean): Builder {
            dialog.isPositiveButtonVisible = visible
            return this
        }

        fun setNegativeButtonVisible(visible: Boolean): Builder {
            dialog.isNegativeButtonVisible = visible
            return this
        }

        fun show(manager: androidx.fragment.app.FragmentManager) {
            // 检查是否已经存在具有相同 tag 的 DialogFragment
            val existingDialog = manager.findFragmentByTag("CommonDialog")
            if (existingDialog == null) {
                dialog.show(manager, "CommonDialog")
            }
        }
    }

}

fun Int.spToPx(context: Context): Float {
    return this * context.resources.displayMetrics.scaledDensity
}