package com.hailiang.composition.ui.practice.theme

import androidx.compose.ui.graphics.Color

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/3/15 18:57
 */
object CompositionColors {

    /**
     * 优点
     */
    val AdvantageBackground = Color(0xFFE2F5FF)
    val AdvantageIndicator = Color(0xFF00C74C)

    /**
     * 建议
     */
    val SuggestionBackground = Color(0xFFF5F0E7)
    val SuggestionIndicator = Color(0xFFE59F00)

    /**
     * 统一的白色背景色
     */
    val WhiteBackground = Color(0xFFFFFFFF)
    val WhiteIndicator = Color(0xFFFFFFFF)

}