package com.hailiang.composition.ui.widget

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.hailiang.common.compose.theme.AppColors
import com.hailiang.composition.ui.practice.PagAnimation
import com.hailiang.xxb.en.composition.R

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/3/18 16:41
 */
class CompositionPlaceHolder {
}

@Composable
fun ComeOnPlaceHolder() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
//        PagAnimation(
//            pagFilePath = "happy.pag", // 指定动画文件名
//            loopCount = 0 // 0表示无限循环播放
//        )
        Image(painter = painterResource(R.drawable.ic_good_job), contentDescription = null)
        Spacer(modifier = Modifier.size(10.dp))
        Text(
            text = "继续加油哦~",
            color = Color(0xFF916108),
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold

        )
    }
}

@Composable
fun GoodJobPlaceHolder() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
//        PagAnimation(
//            pagFilePath = "happy.pag", // 指定动画文件名
//            loopCount = 0 // 0表示无限循环播放
//        )
        Image(painter = painterResource(R.drawable.ic_good_job), contentDescription = null)
        Spacer(modifier = Modifier.size(10.dp))
        Text(
            text = "你已经做得很棒了",
            color = Color(0xFF916108),
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold

        )
    }
}


@Composable
fun ScorePlaceHolder(firstScore: Int, secondScore: Int) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.linearGradient(
                    colorStops = arrayOf(
                        0.0f to Color(0xFFFFFFFF),
                        1.0f to Color(0xFFFFF5D9),
                    ),
                    start = Offset(0F, 0F),
                    end = Offset(0F, Float.POSITIVE_INFINITY)
                )
            ),
        contentAlignment = Alignment.Center
    ) {
        if (firstScore >= secondScore) { // 没进步
            ScoreDownPlaceHolder(secondScore)
        } else {
            ScoreUpPlaceHolder(
                firstScore = firstScore,
                secondScore = secondScore,
            )
        }
    }
}

@Composable
private fun ScoreUpPlaceHolder(firstScore: Int, secondScore: Int) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "预估",
                color = AppColors.TextBlack,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.size(10.dp))
            Text(
                text = "$secondScore",
                fontSize = 42.sp,
                color = Color(0xFFEB5848)
            )
            Spacer(modifier = Modifier.size(10.dp))
            Text(
                text = "分",
                fontSize = 16.sp,
                color = AppColors.TextBlack
            )
        }
        Spacer(modifier = Modifier.size(8.dp))
//        PagAnimation(
//            pagFilePath = "happy.pag", // 指定动画文件名
//            loopCount = 0 // 0表示无限循环播放
//        )
        Image(painter = painterResource(R.drawable.ic_good_job), contentDescription = null)
        Spacer(modifier = Modifier.size(10.dp))
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "比初稿",
                color = AppColors.TextBlack,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold
            )
            Image(
                modifier = Modifier
                    .width(14.dp)
                    .height(20.dp),
                painter = painterResource(R.drawable.ic_score_up), contentDescription = null
            )
            Spacer(modifier = Modifier.size(2.dp))
            Text(
                text = "${secondScore - firstScore}分哦～太棒了",
                color = AppColors.TextBlack,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold
            )
            Image(
                modifier = Modifier
                    .height(22.dp),
                painter = painterResource(R.drawable.ic_thumb_good),
                contentDescription = null
            )

        }
    }
}

@Composable
private fun ScoreDownPlaceHolder(secondScore: Int) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "预估",
                color = AppColors.TextBlack,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.size(10.dp))
            Text(
                text = "$secondScore",
                fontSize = 42.sp,
                color = Color(0xFFEB5848)
            )
            Spacer(modifier = Modifier.size(10.dp))
            Text(
                text = "分",
                fontSize = 16.sp,
                color = AppColors.TextBlack
            )
        }
        Spacer(modifier = Modifier.size(8.dp))
//        PagAnimation(
//            modifier = Modifier.fillMaxSize(),
//            pagFilePath = "happy.pag", // 指定动画文件名
//            loopCount = 0 // 0表示无限循环播放
//        )
        Image(painter = painterResource(R.drawable.ic_good_job_simple), contentDescription = null)
        Spacer(modifier = Modifier.size(10.dp))
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "评分具有主观性，以教师批改结果为准哦~",
                color = AppColors.TextBlack,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold
            )
        }
    }
}

@Composable
fun RetakePlaceHolder(
    modifier: Modifier = Modifier,
    retake: () -> Unit,
    errorMessage: String?,
) {
    ReloadPlaceHolder(
        modifier = modifier,
        reload = retake,
        text = errorMessage ?: "识别失败，请上传清晰、完整的图片哦～",
        btnText = "重 拍"
    )
}

@Composable
fun ReloadPlaceHolder(
    modifier: Modifier = Modifier,
    reload: () -> Unit,
    text: String = "智能批改出错啦",
    btnText: String = "重 试",
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = text,
            color = Color(0xFF5D6E69),
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold
        )
        Spacer(modifier = Modifier.size(2.dp))
        PagAnimation(
            modifier = Modifier.size(186.dp, 100.dp),
            pagFilePath = "error.pag",
            loopCount = 0
        )
//        Image(painter = painterResource(R.drawable.ic_sad), contentDescription = null)
        Spacer(modifier = Modifier.size(6.dp))
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            OutlinedButton(
                modifier = Modifier.width(150.dp),
                onClick = reload,
                border = BorderStroke(
                    width = 1.0.dp,
                    color = Color(0xFF7078F6),
                )
            ) {
                Text(
                    text = btnText,
                    color = Color(0xFF7078F6),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}


@Composable
@Preview(
    widthDp = 800,
    heightDp = 2000,
)
private fun CheckLayoutPreview() {
    Column(modifier = Modifier.background(Color(0xFFFFFFFF))) {
        ComeOnPlaceHolder()
        GoodJobPlaceHolder()
//        ScorePlaceHolder(10, 15)
//        ScorePlaceHolder(15, 15)
        ReloadPlaceHolder(modifier = Modifier.fillMaxSize(), reload = {})
    }
}