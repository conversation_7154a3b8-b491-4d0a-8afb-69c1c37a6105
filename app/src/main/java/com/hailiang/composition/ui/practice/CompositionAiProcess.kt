package com.hailiang.composition.ui.practice

import android.annotation.SuppressLint
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.hailiang.hlutil.HLog
import com.hailiang.xxb.en.composition.R


@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun CompositionAiProcess(
    aiProcessingState: AiProcessingState,
    modifier: Modifier = Modifier
) {
    HLog.i("CompositionAiProcess", "aiProcessingState: $aiProcessingState")
    // 全局进度 (0-100)
    var totalProgress by remember { mutableStateOf(0f) }
    // 记住上次的进度
    var lastProgress by remember { mutableStateOf(0f) }

    // 实际进度值（0-1 范围）
    val actualProgress by remember(totalProgress) {
        derivedStateOf { totalProgress / 100f }
    }

    // 状态切换时设置静态进度，保留上次的进度
    LaunchedEffect(aiProcessingState) {
        val newProgress = when (aiProcessingState) {
            is AiProcessingState.GrammarChecking -> 23f
            is AiProcessingState.EssayGrading -> 56f
            is AiProcessingState.FeedbackGenerating -> 86f
            is AiProcessingState.Success -> 100f
            else -> lastProgress // 未定义状态使用上次的进度
        }
        totalProgress = newProgress
        if (newProgress != lastProgress) {
            lastProgress = newProgress // 更新上次的进度
        }
    }

    Column(modifier = modifier.fillMaxWidth()) {
        // 阶段标签
        BoxWithConstraints(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp)
        ) {
            val totalWidth = maxWidth
            val labelWidth = 90.dp

            StageImage(
                activeImageRes = R.drawable.check_error_active,
                inactiveImageRes = R.drawable.check_error,
                isActive = aiProcessingState !is AiProcessingState.Success,
                isCompleted = actualProgress >= 0.34f || aiProcessingState is AiProcessingState
                .Success,
                modifier = Modifier
                    .width(labelWidth)
                    .align(Alignment.TopStart)
                    .offset(x = totalWidth * 0.28f - labelWidth / 2)
            )
            StageImage(
                activeImageRes = R.drawable.eassy_active,
                inactiveImageRes = R.drawable.eassy,
                isActive = actualProgress in 0.34f..0.66f && aiProcessingState
                        !is AiProcessingState.Success,
                isCompleted = actualProgress >= 0.67f || aiProcessingState is AiProcessingState
                .Success,
                modifier = Modifier
                    .width(labelWidth)
                    .align(Alignment.TopStart)
                    .offset(x = totalWidth * 0.61f - labelWidth / 2)
            )
            StageImage(
                activeImageRes = R.drawable.feedback_active,
                inactiveImageRes = R.drawable.feedback,
                isActive = actualProgress in 0.67f..0.99f && aiProcessingState
                        !is AiProcessingState.Success,
                isCompleted = aiProcessingState is AiProcessingState.Success,
                modifier = Modifier
                    .width(labelWidth)
                    .align(Alignment.TopStart)
                    .offset(x = totalWidth * 0.93f - labelWidth / 2)
            )
        }

        // 自定义进度条
        BoxWithConstraints(
            modifier = Modifier
                .fillMaxWidth()
                .height(12.dp)
                .clip(RoundedCornerShape(8.dp))
        ) {
            val boxWidth = maxWidth
            Canvas(modifier = Modifier.matchParentSize()) {
                val width = size.width
                val height = size.height

                // 绘制背景
                drawRoundRect(
                    color = Color(0xFFCBDEF5),
                    size = Size(width, height),
                    cornerRadius = CornerRadius(16.dp.toPx())
                )

                // 创建渐变画笔
                val gradientBrush = Brush.horizontalGradient(
                    colors = listOf(
                        Color(0xFF2BA8FF),
                        Color(0xFF2588F7),
                        Color(0xFF1759EE)
                    )
                )

                // 绘制进度，使用 actualProgress
                drawRoundRect(
                    brush = gradientBrush,
                    size = Size(width * actualProgress.coerceIn(0f, 1f), height),
                    cornerRadius = CornerRadius(8.dp.toPx())
                )
            }

            // 进度条头部图片
            if (totalProgress > 3) {
                PagAnimation(
                    modifier = Modifier
                        .size(32.dp)
                        .offset(x = boxWidth * actualProgress.coerceIn(0f, 1f) - 36.dp, y = 0.dp)
                        .align(Alignment.TopStart),
                    pagFilePath = "ai_progress_head.pag",
                    loopCount = 0,
                )
            }
        }
    }
}

@Composable
private fun StageImage(
    activeImageRes: Int,
    inactiveImageRes: Int,
    isActive: Boolean,
    isCompleted: Boolean,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(
                id = if (isActive || isCompleted) activeImageRes else inactiveImageRes
            ),
            contentDescription = null,
            modifier = Modifier.size(width = 68.dp, height = 22.dp)
        )
        if (isCompleted) {
            Spacer(modifier = Modifier.width(4.dp))
            Image(
                painter = painterResource(id = R.drawable.complate),
                contentDescription = null,
                modifier = Modifier.size(14.dp)
            )
        }
    }
}

private fun isErrorState(state: AiProcessingState): Boolean {
    return state is AiProcessingState.ContentRecognitionFailed ||
            state is AiProcessingState.ServiceError ||
            state is AiProcessingState.NetworkError
}

@Preview(showBackground = true)
@Composable
fun CompositionAiProcessPreview() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White)
            .padding(16.dp)
    ) {
        CompositionAiProcess(
            aiProcessingState = AiProcessingState.GrammarChecking,
            modifier = Modifier.fillMaxWidth()
        )
    }
}