package com.hailiang.composition.ui.practice

import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import com.hailiang.composition.data.bean.CompositionCheckBean.Advice
import com.hailiang.composition.data.bean.CompositionCheckBean.Allusion
import com.hailiang.composition.data.bean.CompositionCheckBean.ComprehensiveJudge
import com.hailiang.composition.data.bean.CompositionCheckBean.Judge
import com.hailiang.composition.data.bean.request.FeedbackType
import com.hailiang.composition.ui.practice.theme.CompositionColors
import com.hailiang.composition.ui.widget.ComeOnPlaceHolder
import com.hailiang.composition.ui.widget.CompositionEnTableWidget
import com.hailiang.composition.ui.widget.GoodJobPlaceHolder
import com.hailiang.question.en.composition.EnComposition
import com.hailiang.xxb.en.composition.R


/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/3/13 22:26
 */
sealed class AiSector(
    val name: String,
    val normalRes: Int,
    val selectedRes: Int,
    val id: String,
    val placeHolder: (@Composable () -> Unit)? = null,
    val feedbackType: FeedbackType,
) {
    object Evaluate : AiSector(
        name = "综合评价",
        id = "Evaluate",
        normalRes = R.drawable.icon_evaluate_normal,
        selectedRes = R.drawable.icon_evaluate_selected,
        placeHolder = {
            GoodJobPlaceHolder()
        },
        feedbackType = FeedbackType.OverallMerit
    )

    object Dibble : AiSector(
        name = "点拨",
        id = "Dibble",
        normalRes = R.drawable.icon_dibble_normal,
        selectedRes = R.drawable.icon_dibble_selected,
        placeHolder = {
            GoodJobPlaceHolder()
        },
        feedbackType = FeedbackType.GiveAdvice

    )

    object Petrol : AiSector(
        name = "加油站",
        id = "Petrol",
        normalRes = R.drawable.icon_petrol_normal,
        selectedRes = R.drawable.icon_petrol_selected,
        placeHolder = null,
        feedbackType = FeedbackType.KnowledgeGasStation
    )

    class OnePage(
        correctedComposition: EnComposition? = null,
        compositionTableState: CompositionTableState? = null
    ) :
        AiSector(
            name = "一稿",
            id = "OnePage",
            normalRes = R.drawable.icon_page_normal,
            selectedRes = R.drawable.icon_page_selected,
            placeHolder = {
                if (compositionTableState is CompositionTableState.Success) {
                    CompositionEnTableWidget(
                        composition = correctedComposition ?: compositionTableState.composition,
                    )
                }
            },
            feedbackType = FeedbackType.OnePage
        )

    private fun addComprehensiveJudgeItems(
        comprehensiveJudge: ComprehensiveJudge?, aiSubSectorList: MutableList<AiSubSector>
    ) {
        if (comprehensiveJudge?.advantage.isNullOrEmpty() && comprehensiveJudge?.suggestion.isNullOrEmpty()) {
            return
        }
        comprehensiveJudge?.advantage?.let {
            aiSubSectorList.add(
                AiSubSector.Advantage(
                    comments = if (it.isEmpty()) emptyList() else listOf(
                        AiSectorComment.Advantage(
                            comment = it
                        )
                    )
                )
            )
        }
        comprehensiveJudge?.suggestion?.let {
            aiSubSectorList.add(
                AiSubSector.Suggestion(
                    comments = if (it.isEmpty()) emptyList() else listOf(
                        AiSectorComment.Suggestion(
                            comment = it
                        )
                    )
                )
            )
        }
    }

    fun getAiSubSectorList(
        comprehensiveJudge: ComprehensiveJudge?,
        adviceList: List<Advice>?,
        allusionList: List<Allusion>?,
    ): List<AiSubSector> {
        val aiSubSectorList = mutableListOf<AiSubSector>()
        when (this) {
            Evaluate -> {
                addComprehensiveJudgeItems(comprehensiveJudge, aiSubSectorList)
//                if (comprehensiveJudge?.advantage.isNullOrEmpty() && comprehensiveJudge?.suggestion.isNullOrEmpty()) {
//                    return aiSubSectorList
//                }
//                comprehensiveJudge?.advantage?.let {
//                    aiSubSectorList.add(
//                        AiSubSector.Advantage(
//                            comments = if (it.isEmpty()) emptyList() else listOf(
//                                AiSectorComment.Advantage(
//                                    comment = it
//                                )
//                            )
//                        )
//                    )
//                }
//                comprehensiveJudge?.suggestion?.let {
//                    aiSubSectorList.add(
//                        AiSubSector.Suggestion(
//                            comments = if (it.isEmpty()) emptyList() else listOf(
//                                AiSectorComment.Suggestion(
//                                    comment = it
//                                )
//                            )
//                        )
//                    )
//                }
            }

            Dibble -> {
                // 先添加综合评定数据
                addComprehensiveJudgeItems(comprehensiveJudge, aiSubSectorList)
                // 再添加原有点拨数据
                adviceList?.forEach { advice ->
                    AiSubSector.obtainDibble(advice.type, advice.judgeList)?.let {
                        aiSubSectorList.add(it)
                    }
                }
            }

            Petrol -> {
                allusionList?.forEach { allusion ->
                    AiSubSector.obtainPetrol(allusion.type, allusion.content)?.let {
                        aiSubSectorList.add(it)
                    }
                }
            }

            is OnePage -> {

            }
        }
        return aiSubSectorList
    }
}


/**
 * 子块
 */
sealed class AiSubSector(
    val name: String,
    val comments: List<AiSectorComment>,
    val iconRes: Int? = null,
    val needIndicator: Boolean = true,
    val placeHolder: (@Composable () -> Unit)? = null,
    val backgroundColor: Color = CompositionColors.WhiteBackground,
) {
    class Advantage(
        comments: List<AiSectorComment>,
    ) : AiSubSector(
        name = "文章优点", comments = comments,
//        iconRes = R.drawable.icon_advantage,
        needIndicator = false, placeHolder = {
            ComeOnPlaceHolder()
        }, backgroundColor = CompositionColors.AdvantageBackground
    )

    class Suggestion(
        comments: List<AiSectorComment>,
    ) : AiSubSector(
        name = "提升建议",
        comments = comments,
        needIndicator = false,
        iconRes = R.drawable.icon_suggest,
        placeHolder = {
            GoodJobPlaceHolder()
        },
        backgroundColor = CompositionColors.SuggestionBackground
    ) {}

    // ----------------------------------------------------------------------
    class Title(
        comments: List<AiSectorComment>,
    ) : AiSubSector(
        name = "标题",
        comments = comments,
        needIndicator = false,
        iconRes = R.drawable.icon_title,
    )

    class Structure(
        comments: List<AiSectorComment>,
    ) : AiSubSector(
        name = "篇章结构",
        comments = comments,
        iconRes = R.drawable.icon_structure,
        backgroundColor = CompositionColors.WhiteBackground
    )

    class Argument(
        comments: List<AiSectorComment>,
    ) : AiSubSector(
        name = "论点论据",
        comments = comments,
        iconRes = R.drawable.icon_argument,
    )

    class BeginningAndEnd(
        comments: List<AiSectorComment>,
    ) : AiSubSector(
        name = "开头结尾",
        comments = comments,
        iconRes = R.drawable.icon_beginning_and_end,
    )

    class Sentence(
        comments: List<AiSectorComment>,
    ) : AiSubSector(
        name = "句子",
        comments = comments,
        iconRes = R.drawable.icon_sentence,
    )

    class Language(
        comments: List<AiSectorComment>,
    ) : AiSubSector(
        name = "语言",
        comments = comments,
        iconRes = R.drawable.icon_language, // 可以后续更换为专门的语言图标
    )

    class Content(
        comments: List<AiSectorComment>,
    ) : AiSubSector(
        name = "内容",
        comments = comments,
        iconRes = R.drawable.icon_content, // 可以后续更换为专门的内容图标
    )

    class History(
        comments: List<AiSectorComment>,
    ) : AiSubSector(
        name = "历史典故",
        comments = comments,
        needIndicator = false,
        iconRes = R.drawable.icon_ai_history,
    )

    class Reality(
        comments: List<AiSectorComment>,
    ) : AiSubSector(
        name = "现实案例",
        comments = comments,
        needIndicator = false,
        iconRes = R.drawable.icon_ai_reality,
    )

    class RefinedSentence(
        comments: List<AiSectorComment>,
    ) : AiSubSector(
        name = "名人名言/诗句",
        comments = comments,
        needIndicator = false,
        iconRes = R.drawable.icon_ai_refinedsentence,
    )

    class ShortItem(
        comments: List<AiSectorComment>,
    ) : AiSubSector(
        name = "短语",
        comments = comments,
        needIndicator = false,
        iconRes = R.drawable.icon_short_world, // 可以后续更换为专门的短语图标
    )

    class LongSentence(
        comments: List<AiSectorComment>,
    ) : AiSubSector(
        name = "长句",
        comments = comments,
        needIndicator = false,
        iconRes = R.drawable.icon_sentence, // 可以后续更换为专门的长句图标
    )

    companion object {
        fun obtainDibble(type: String?, comments: List<Judge>?): AiSubSector? {
            if (comments.isNullOrEmpty()) return null
            return when (type?.lowercase()) {
                "title" -> Title(comments = fillComment(comments))
                "structure" -> Structure(comments = fillComment(comments))
                "argument" -> Argument(comments = fillComment(comments))
                "beginningandend" -> BeginningAndEnd(comments = fillComment(comments))
                "sentence" -> Sentence(comments = fillComment(comments))
                "language" -> Language(comments = fillComment(comments))
                "content" -> Content(comments = fillComment(comments))
                else -> return null
            }
        }

        fun obtainPetrol(type: String?, content: String?): AiSubSector? {
            if (content.isNullOrEmpty()) return null
            return when (type?.lowercase()) {
                "history" -> History(
                    comments = listOf(
                        AiSectorComment.Normal(
                            content = content, comment = content
                        )
                    )
                )

                "reality" -> Reality(
                    comments = listOf(
                        AiSectorComment.Normal(
                            content = content, comment = content
                        )
                    )
                )

                "refinedsentence" -> RefinedSentence(
                    comments = listOf(
                        AiSectorComment.Normal(
                            content = content, comment = content
                        )
                    )
                )
                // 新增的类型支持 - 根据接口返回的实际类型
                "shortitem" -> ShortItem(
                    comments = listOf(
                        AiSectorComment.Normal(
                            content = content, comment = content
                        )
                    )
                )

                "longsentence" -> LongSentence(
                    comments = listOf(
                        AiSectorComment.Normal(
                            content = content, comment = content
                        )
                    )
                )

                else -> return null
            }
        }

        private fun fillComment(judges: List<Judge>): List<AiSectorComment> {
            val sectorContents = mutableListOf<AiSectorComment>()
            judges.forEach { judge ->
                judge.advantage?.let {
                    sectorContents.add(
                        AiSectorComment.Advantage(
                            content = judge.content, comment = it
                        )
                    )
                }
                judge.suggestion?.let {
                    sectorContents.add(
                        AiSectorComment.Suggestion(
                            content = judge.content, comment = it
                        )
                    )
                }
            }
            return sectorContents
        }
    }
}

/**
 * AI 点拨的评论，优点、建议
 */
sealed class AiSectorComment(
    val name: String,
    val content: String?,
    val comment: String,
    val indicatorColor: Color,
    val backgroundColor: Color,
) {

    class Advantage(
        content: String? = null,
        comment: String,
    ) : AiSectorComment(
        name = "优点",
        content = content,
        comment = comment,
        indicatorColor = CompositionColors.AdvantageIndicator,
        backgroundColor = CompositionColors.AdvantageBackground,
    )

    class Suggestion(
        content: String? = null,
        comment: String,
    ) : AiSectorComment(
        name = "建议",
        content = content,
        comment = comment,
        indicatorColor = CompositionColors.SuggestionIndicator,
        backgroundColor = CompositionColors.SuggestionBackground,
    )

    class Normal(
        content: String? = null,
        comment: String,
    ) : AiSectorComment(
        name = "",
        content = content,
        comment = comment,
        backgroundColor = CompositionColors.AdvantageBackground,
        indicatorColor = CompositionColors.AdvantageIndicator,
    )
}

