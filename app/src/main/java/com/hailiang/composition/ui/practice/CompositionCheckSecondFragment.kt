package com.hailiang.composition.ui.practice

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.lifecycleScope
import com.hailiang.common.base.BaseFragment
import com.hailiang.composition.ui.widget.CompositionCheckSecondLayout
import com.hailiang.composition.ui.widget.CompositionEnTableWidget
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.hlutil.dp
import com.hailiang.hlutil.ext.clipOutline
import com.hailiang.view.question.composition.CompositionIndex
import com.hailiang.view.question.composition.CompositionTableLayout
import com.hailiang.view.question.composition.bean.CompositionSentenceInfo
import com.hailiang.xxb.en.composition.R
import com.hailiang.xxb.en.composition.databinding.FragmentCompositionCheckSecondBinding
import kotlinx.coroutines.launch

/**
 * Description: 查看二次作答
 *
 * <AUTHOR>
 * @version 2025/2/21 10:58
 */
class CompositionCheckSecondFragment : BaseFragment(R.layout.fragment_composition_check_second) {

    private var _binding: FragmentCompositionCheckSecondBinding? = null
    private val binding get() = _binding!!

    private val checkViewModel by activityViewModels<CompositionCheckViewModel>()
    private val compositionViewModel by activityViewModels<CompositionViewModel>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = FragmentCompositionCheckSecondBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.compositionStudentContentContainer.clipOutline(15.dp)
        binding.compositionTeacherCheckContainer.clipOutline(15.dp)
        binding.compositionStudentContentContainer.let {
            it.removeAllViews()
            it.addView(ComposeView(requireActivity()).apply {
                setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
                setContent {
                    val compositionSecondCorrectTableState by compositionViewModel.compositionSecondCorrectTableState.collectAsStateWithLifecycle()
                    val scoreState by compositionViewModel.scoreState.collectAsStateWithLifecycle()
                    val aiResponseState by compositionViewModel.aiResponseState.collectAsStateWithLifecycle()
                    var score by remember { mutableStateOf(-1f) }
                    LaunchedEffect(scoreState) {
                        scoreState.let { status ->
                            score = scoreState
                        }
                    }
                    CompositionEnTableWidget(
                        composition = compositionSecondCorrectTableState,
                        score = score.toInt(),
                        editable = false,
                        aiResponseState = aiResponseState,
                    )
                }
            })
        }

        // ----------------------------------------------------------------------
        binding.compositionTeacherCheckContainer.removeAllViews()
        binding.compositionTeacherCheckContainer.addView(
            ComposeView(requireActivity()).apply {
                setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
                setContent {
                    CompositionCheckSecondLayout(
                        compositionViewModel = compositionViewModel,
                        checkViewModel = checkViewModel,
                    )
                }
            })
    }


    override fun onDestroyView() {
        super.onDestroyView()
//        compositionViewModel.stopLoopAiResult()
        _binding = null
    }
}