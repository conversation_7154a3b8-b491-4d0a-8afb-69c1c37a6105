package com.hailiang.composition.ui.main

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.ViewStub
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.hailiang.common.base.BaseActivity
import com.hailiang.common.event.FlowEventBus
import com.hailiang.common.event.receiver.TimeChangeReceiver
import com.hailiang.common.event.receiver.TimeTickEvent
import com.hailiang.common.ext.recycleriew.GridSpacingItemDecoration
import com.hailiang.common.util.Constants
import com.hailiang.composition.data.Repository
import com.hailiang.composition.data.bean.CreateWorkBean
import com.hailiang.composition.data.bean.TaskBean
import com.hailiang.composition.data.bean.TaskMaterialBean
import com.hailiang.composition.data.bean.WorkInfo
import com.hailiang.composition.dialog.CommonDialog
import com.hailiang.composition.ui.TestActivity
import com.hailiang.composition.ui.guidance.TakePhotoGuidanceActivity
import com.hailiang.composition.ui.photograph.PhotoViewModel
import com.hailiang.composition.ui.photograph.TakePhotoCompositionActivity
import com.hailiang.composition.ui.practice.CompositionGuidanceActivity
import com.hailiang.core.widget.AvatarHelper
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.dpInt
import com.hailiang.hlutil.id
import com.hailiang.opensdk.LauncherData
import com.hailiang.opensdk.consts.UserType
import com.hailiang.recyclerview.adapter.BaseRecyclerViewAdapter
import com.hailiang.recyclerview.adapter.listener.OnItemClickListener
import com.hailiang.xxb.en.composition.BuildConfig
import com.hailiang.xxb.en.composition.R
import com.hailiang.xxb.refresh.SmartRefreshLayout
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.Locale

class MainActivity : BaseActivity() {
    private val ivAvatar: ImageView by id(R.id.iv_avatar)
    private val tvName: TextView by id(R.id.tv_name)
    private val tvTime: TextView by id(R.id.tv_time)
    private val vsEmpty: ViewStub by id(R.id.vs_empty)
    private val refreshLayout: SmartRefreshLayout by id(R.id.smart_refresh)
    private val recyclerView: RecyclerView by id(R.id.rv)
    private val btnAdd: View by id(R.id.ll_write)
    private val adapter = BaseRecyclerViewAdapter(CompositionViewPresenter())
    private val tvPhotoExample: TextView by id(R.id.tv_photo_example)
    private val timeChangeReceiver = TimeChangeReceiver()
    private val viewModel by viewModels<MainViewModel>()
    private val takePhotoGuideLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { _ ->
            HLog.i("MainActivity", "takePhotoGuideLauncher: result")
            btnAdd.callOnClick()
        }
    private val takePhotoLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            HLog.i("MainActivity", "Photo taken successfully")
            if (result.resultCode == RESULT_OK) {
                result.data?.let { intent ->
                    val WORK_ID = "workId"
                    val WORK_STATE_ID = "workStateId"
                    val workId = intent.getLongExtra(WORK_ID, 0)
                    val workStateId = intent.getLongExtra(WORK_STATE_ID, 0)
                    CompositionGuidanceActivity.start(
                        this, workId, workStateId
                    )
                }
            }
        }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        timeChangeReceiver.register(this)
        //加载词库
        viewModel.getTextCorrectionInfo(this@MainActivity)
    }

    override fun onDestroy() {
        super.onDestroy()
        timeChangeReceiver.unregister(this)
    }

    override fun onResume() {
        super.onResume()
        if (vsEmpty.isVisible) {
            viewModel.getCompositionList(true)
        } else {
            refreshLayout.autoRefresh()
        }
    }

    override fun initView() {
        setContentView(R.layout.activity_main)

        AvatarHelper.load(
            ivAvatar, LauncherData.getUserInfo()?.headUrl, UserType.STUDENT, LauncherData.getUserInfo()?.sex ?: 0
        )
//        tvName.text = "HI, " + Constants.username
        tvName.text = "HI, ${Constants.username}"

        recyclerView.layoutManager = GridLayoutManager(this, 3)
        recyclerView.addItemDecoration(GridSpacingItemDecoration(0.dpInt, 15.dpInt))
        recyclerView.adapter = adapter
        adapter.setOnItemClickListener(object : OnItemClickListener {
            override fun onItemClick(v: View, data: Any, position: Int) {
                val workInfo = data as WorkInfo
                CompositionGuidanceActivity.start(
                    this@MainActivity, workInfo.schoolworkId, workInfo.schoolworkStateId
                )
            }
        })
        adapter.addOnItemChildClickListener(R.id.iv_delete, object : OnItemClickListener {
            override fun onItemClick(v: View, data: Any, position: Int) {
                CommonDialog.Builder().setTitle("删除").setContentText("删除后无法恢复，确定删除吗？")
                    .setNegativeText("再想想").setPositiveAction {
                        val workInfo = data as WorkInfo
                        viewModel.deleteComposition(workInfo.schoolworkStateId) {
                            viewModel.getCurrentIndexCompositionList()
                            adapter.remove(data)
                            if (adapter.dataList.isEmpty()) {
                                refreshLayout.autoRefresh()
                            }
                        }
                    }.show(supportFragmentManager)

            }
        })
        btnAdd.setOnClickListener {
            viewModel.requestBeginnerGuidance(guideBlock = {
                takePhotoGuideLauncher.launch(
                    Intent(this@MainActivity, TakePhotoGuidanceActivity::class.java)
                )
            }, photoBlock = {
//                TakePhotoCompositionActivity.actionStart(
//                    this@MainActivity, 0, 0, PhotoViewModel.TIME_FIRST
//                )

                takePhotoLauncher.launch(
                    Intent(this@MainActivity, TakePhotoCompositionActivity::class.java).apply {
                        putExtra("timeType", PhotoViewModel.TIME_FIRST)
                        intent.putExtra("workId", 0)
                        intent.putExtra("workStateId", 0)
                        intent.putExtra("time", PhotoViewModel.TIME_FIRST)
                    })
            })
        }
        tvPhotoExample.setOnClickListener {
            startActivity(Intent(this@MainActivity, TakePhotoGuidanceActivity::class.java))
        }

        refreshLayout.setEnableAutoLoadMore(true)
        refreshLayout.setOnRefreshListener {
            refreshLayout.resetNoMoreData()
            viewModel.getCompositionList(true)
        }
        refreshLayout.setOnLoadMoreListener {
            viewModel.getCompositionList(false)
        }

        //fixme 测试
        tvName.setOnClickListener {
            if (BuildConfig.DEBUG) {
                val createWorkBean = CreateWorkBean().apply {
                    compositionImageList =
                        listOf("https://jzjx-dev-resource.hailiangedu.com/workcloud/2025/07/17/CROP_120250717144607025.jpeg")
                    taskList = listOf(
                        TaskBean(
                            id = -1, submitType = "takePhoto", taskMaterialList = listOf(
                                TaskMaterialBean(
                                    materialFileType = "picture",
                                    materialType = "task",
                                    materialFileUrl = "https://jzjx-dev-resource.hailiangedu.com/workcloud/2025/07/17/CROP_120250717144543523.jpeg"
                                )
                            )
                        )
                    )
                }
                lifecycleScope.launch {
                    Repository().addComposition(createWorkBean)
                }
            }
        }
    }

    override fun observeData() {
        FlowEventBus.observeEventT<TimeTickEvent>(this) {
            updateTime()
        }
        viewModel.compositionListLiveData.observe(this) {
            refreshLayout.finishRefresh(true)
            if ((it == null || it.list.isEmpty())) {
                if (adapter.itemCount == 0) {
                    vsEmpty.visibility = View.VISIBLE
                    refreshLayout.visibility = View.GONE
                }
                refreshLayout.finishLoadMoreWithNoMoreData()
                return@observe
            }
            refreshLayout.visibility = View.VISIBLE
            refreshLayout.finishLoadMore(0)
            vsEmpty.visibility = View.GONE
            if (it.currPage == 1) {
                adapter.clear()
            }
            /**
             * 此时可以添加一个值的函数  看一下 it.list 的item 有没有在 adapter.dataList 中存在
             * 如果存在 就不添加  如果不存在 就添加
             */
            if (it.currPage == 1) {
                adapter.addAll(it.list)
                return@observe
            }
            val newList = it.list.filter { item ->
                !adapter.dataList.any { adapterItem ->
                    (adapterItem as? WorkInfo)?.schoolworkStateId == item.schoolworkStateId
                }
            }
            if (newList.isNotEmpty()) {
                adapter.addAll(newList)
            }
        }
    }

    override fun initData() {
        updateTime()
    }

    @SuppressLint("SetTextI18n")
    private fun updateTime() {
        val now = LocalDateTime.now()
        val timeFormatter = DateTimeFormatter.ofPattern("HH:mm", Locale.getDefault())
        val dayFormatter = DateTimeFormatter.ofPattern("E", Locale.CHINA)
        tvTime.text = "${now.format(timeFormatter)} ${now.format(dayFormatter)}"
    }

}