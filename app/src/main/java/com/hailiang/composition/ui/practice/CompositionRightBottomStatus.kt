package com.hailiang.composition.ui.practice

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.hailiang.xxb.en.composition.R

@Composable
fun CompositionRightBottomStatus(
    aiProcessingState: AiProcessingState,
    modifier: Modifier = Modifier,
) {
    // 使用 remember 缓存不变的 Modifier，避免重复创建
    val containerModifier = remember {
        modifier.size(360.dp, 168.dp)
    }

    Box(modifier = containerModifier.offset(x = 168.dp)) {
        Row(
            modifier = Modifier.fillMaxSize(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // 状态文案区域 - 使用独立组件避免整体重组
//            StatusTextBubble(
//                aiProcessingState = aiProcessingState,
//                modifier = Modifier
//                    .size(width = 182.dp, height = 86.dp)
//                    .offset(x = 46.dp, y = -10.dp)
//            )

            // 动画区域 - 使用独立组件避免整体重组
            StatusAnimation(
                aiProcessingState = aiProcessingState,
                modifier = Modifier
                    .width(140.dp)
                    .height(100.dp)
            )
        }
    }
}

@Composable
private fun StatusTextBubble(
    aiProcessingState: AiProcessingState,
    modifier: Modifier = Modifier
) {
    // 缓存不变的样式（不包含weight，因为它需要在RowScope中使用）
    val bubbleBaseModifier = remember {
        Modifier
            .shadow(
                elevation = 15.dp,
                shape = RoundedCornerShape(10.dp),
                ambientColor = Color(0x40000000),
                spotColor = Color(0x45000000)
            )
            .background(
                color = Color.White,
                shape = RoundedCornerShape(8.dp)
            )
    }

    Row(modifier = modifier) {
        Box(modifier = bubbleBaseModifier.weight(1f)) {
            Column(
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 使用独立的文本内容组件，只有文本变化时才重组
                StatusTextContent(aiProcessingState = aiProcessingState)
            }
        }

        // 气泡三角形 - 使用 remember 缓存
        BubbleTriangle()
    }
}

@Composable
private fun StatusTextContent(aiProcessingState: AiProcessingState) {
    when (aiProcessingState) {
        is AiProcessingState.Idle -> {
            // 空状态
        }

        is AiProcessingState.RecognizingContent -> {
            Text(
                text = stringResource(id = R.string.ai_status_recognizing),
                fontSize = 16.sp,
                color = Color.Black,
                style = TextStyle(fontWeight = FontWeight.Bold)
            )
            Text(
                text = stringResource(id = R.string.ai_status_recognizing_hint),
                fontSize = 14.sp,
                color = Color(0xFF515A6E),
                modifier = Modifier.padding(top = 2.dp)
            )
        }

        is AiProcessingState.ContentRecognitionFailed -> {
            Text(
                text = stringResource(id = R.string.ai_status_failed),
                fontSize = 16.sp,
                color = Color.Black,
                style = TextStyle(fontWeight = FontWeight.Bold)
            )
        }

        is AiProcessingState.GrammarChecking -> {
            Text(
                text = stringResource(id = R.string.ai_status_correcting),
                fontSize = 16.sp,
                color = Color.Black,
                style = TextStyle(fontWeight = FontWeight.Bold)
            )
            Text(
                text = stringResource(id = R.string.ai_status_feedback_ready),
                fontSize = 14.sp,
                color = Color(0xFF515A6E),
                modifier = Modifier.padding(top = 2.dp, bottom = 2.dp)
            )
            Text(
                text = stringResource(id = R.string.ai_status_feedback_1),
                fontSize = 14.sp,
                color = Color(0xFF515A6E)
            )
        }

        is AiProcessingState.EssayGrading -> {
            Text(
                text = stringResource(id = R.string.ai_status_correc_over),
                fontSize = 14.sp,
                color = Color(0xFF515A6E)
            )
            Text(
                text = stringResource(id = R.string.ai_status_feedback_loading),
                fontSize = 16.sp,
                color = Color.Black,
                style = TextStyle(fontWeight = FontWeight.Bold),
                modifier = Modifier.padding(top = 2.dp, bottom = 2.dp)
            )
            Text(
                text = stringResource(id = R.string.ai_status_feedback_1),
                fontSize = 14.sp,
                color = Color(0xFF515A6E)
            )
        }

        is AiProcessingState.FeedbackGenerating -> {
            Text(
                text = stringResource(id = R.string.ai_status_correc_over),
                fontSize = 14.sp,
                color = Color(0xFF515A6E)
            )
            Text(
                text = stringResource(id = R.string.ai_status_feedback_over),
                fontSize = 14.sp,
                color = Color(0xFF515A6E),
                modifier = Modifier.padding(top = 2.dp, bottom = 2.dp)
            )
            Text(
                text = stringResource(id = R.string.ai_status_feedback_1_ing),
                fontSize = 16.sp,
                color = Color.Black,
                style = TextStyle(fontWeight = FontWeight.Bold)
            )
        }

        is AiProcessingState.ServiceError -> {
            Text(
                text = stringResource(id = R.string.ai_status_error),
                fontSize = 16.sp,
                color = Color.Black,
                style = TextStyle(fontWeight = FontWeight.Bold)
            )
        }

        is AiProcessingState.NetworkError -> {
            Text(
                text = stringResource(id = R.string.ai_network_error),
                fontSize = 16.sp,
                color = Color.Black,
                style = TextStyle(fontWeight = FontWeight.Bold)
            )
        }

        else -> {
            // 其他状态
        }
    }
}


@Composable
private fun BubbleTriangle() {
    val trianglePath = remember {
        Path().apply {
            moveTo(0f, 30f)
            lineTo(0f, 70f)
            lineTo(40f, 70f)
            close()
        }
    }

    Canvas(modifier = Modifier.size(20.dp)) {
        drawPath(trianglePath, color = Color.White)
    }
}

@Composable
private fun StatusAnimation(
    aiProcessingState: AiProcessingState,
    modifier: Modifier = Modifier
) {
    // 根据状态决定动画文件，避免不必要的重组
    val animationFile = remember(aiProcessingState) {
        when (aiProcessingState) {
            is AiProcessingState.ContentRecognitionFailed -> "error.pag"
            else -> "thinking.pag"
        }
    }

    Box(modifier = modifier) {
        PagAnimation(
            modifier = Modifier.fillMaxSize(),
            pagFilePath = animationFile,
            loopCount = 0
        )
    }
}


/**
 * 优化版本的CompositionRightBottomStatus，使用更细粒度的状态管理
 * 主要优化点：
 * 1. 使用remember缓存不变的Modifier和Path
 * 2. 将组件拆分为更小的独立组件，减少重组范围
 * 3. 使用remember(key)确保只有相关状态变化时才重新计算
 * 4. 避免在每次重组时重新创建复杂对象
 */

@Preview
@Composable
fun CompositionRightBottomStatusPreview() {
    CompositionRightBottomStatus(
        aiProcessingState = AiProcessingState.RecognizingContent,
    )
}