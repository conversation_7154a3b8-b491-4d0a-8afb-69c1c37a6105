package com.hailiang.composition.ui.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import com.hailiang.composition.data.bean.CompositionCheckBean
import com.hailiang.composition.data.enums.JobStatus
import com.hailiang.composition.ui.practice.AiProcessingState
import com.hailiang.composition.ui.practice.AiResponseState
import com.hailiang.composition.ui.practice.AiSector
import com.hailiang.composition.ui.practice.AiSubSector
import com.hailiang.composition.ui.practice.EvaluateFeedback
import com.hailiang.composition.ui.practice.EvaluateFeedbackState
import com.hailiang.composition.ui.practice.theme.CompositionFonts
import com.hailiang.markdown.MarkDownCommentView
import com.hailiang.xxb.en.composition.R

/**
 * Description: AI 识别结果：综合评价、点拨、加油站
 *
 * <AUTHOR>
 * @version 2025/4/11 10:55
 */
@Composable
fun AiResultLayout(
    aiResponseState: AiResponseState,
    aiProcessingState: AiProcessingState? = null,
    aiTabList: List<AiSector>,
    switchAiTab: (AiSector) -> Unit,
    retry: () -> Unit,
    reload: () -> Unit,
    retake: (() -> Unit)? = null,
    footer: (@Composable () -> Unit)? = null,
    bottomBar: @Composable () -> Unit,
    sentenceClickIndex: Int = -1,
    aiStatus: String? = "first",
    initFeedback: EvaluateFeedbackState? = null,
    feedbackClick: ((AiSector, EvaluateFeedback, Boolean) -> Unit)? = null,
    source: String? = ""
) {
    when (aiResponseState) {
        is AiResponseState.Default -> {
            // 默认状态：显示加载动画
            LoadingAnimation()
        }

        is AiResponseState.Success -> {
            // 成功状态：展示 AI 识别结果
            // 新的 allSuccess 计算逻辑
            val calculatedAllSuccess =
                if (source.isNullOrEmpty()) { // (1) 检查 source 是否为默认值 (null 或空字符串)
                    // 如果 source 是默认值，则使用原始的成功状态判断
                    aiResponseState.selectedJobStatus.isSuccess() &&
                            aiResponseState.scoreJobStatus.isSuccess() &&
                            aiResponseState.allJobStatus.isSuccess()
                } else {
                    // 如果 source 不是默认值，则 allSuccess 强制为 false
                    false // (2)
                }
            AiSuccessLayout(
                sectorList = aiTabList,
                selectedSector = aiResponseState.selectedAiSector,
                sectorJobStatus = aiResponseState.selectedJobStatus,
                scoreJobStatus = aiResponseState.scoreJobStatus,
                subSectors = aiResponseState.aiSubSectorList,
                comprehensiveJudge = aiResponseState.comprehensiveJudge,
                adviceList = aiResponseState.adviceList,
                allusionList = aiResponseState.allusionList,
                score = aiResponseState.score,
                totalScore = aiResponseState.totalScore,
                switchAiTab = switchAiTab,
                allSuccess = calculatedAllSuccess,
                footer = footer,
                bottomBar = bottomBar,
                sentenceClickIndex = sentenceClickIndex,
                aiStatus = aiStatus,
                initFeedback = initFeedback,
                feedbackClick = feedbackClick,
                source = source
            )
        }

        is AiResponseState.Reload -> {
            // 重新加载状态：显示 ReloadPlaceHolder
            ReloadPlaceHolder(modifier = Modifier.fillMaxSize(), reload = reload)
        }

        is AiResponseState.Retry -> {
            // 重试状态：显示 ReloadPlaceHolder
            ReloadPlaceHolder(modifier = Modifier.fillMaxSize(), reload = retry)
        }

        is AiResponseState.AiRequestError -> {
            // AI 请求错误：显示 AiSteamLayout 并允许重试
            AiSteamLayout(
                aiStreamDetail = aiResponseState.aiStreamDetail,
                doRetry = reload,
                aiProcessingState = AiProcessingState.ServiceError
            )
        }

        is AiResponseState.AiStreamError -> {
            // AI 流错误：显示 AiSteamLayout 并根据错误类型决定是重载还是重试
            AiSteamLayout(
                aiStreamDetail = aiResponseState.aiStreamDetail, doRetry = {
                    if (aiResponseState.aiStreamDetail.isNetworkError()) {
                        reload()
                    } else {
                        retry()
                    }
                }, aiProcessingState = AiProcessingState.ServiceError
            )
        }

        is AiResponseState.AiStreaming -> {
            // AI 正在流式处理：显示 AiSteamLayout
            AiSteamLayout(
                aiStreamDetail = aiResponseState.aiStreamDetail, doRetry = {
                    if (aiResponseState.aiStreamDetail.isNetworkError()) {
                        reload()
                    } else {
                        retry()
                    }
                }, aiProcessingState = aiProcessingState
            )
        }

        is AiResponseState.AiStreamPreparing -> {
            // AI 流准备中：检查是否有预览详情，如果没有则显示加载动画
            val preDetail = aiResponseState.preAiStreamDetail
            if (preDetail == null) {
                LoadingAnimation()
            } else {
                AiSteamLayout(
                    aiStreamDetail = preDetail, doRetry = {
                        if (preDetail.isNetworkError()) {
                            reload()
                        } else {
                            retry()
                        }
                    }, aiProcessingState = aiProcessingState
                )
            }
        }

        is AiResponseState.OcrLoading -> {
            // OCR 加载中：如果不是题目，则显示 OcrLoadingAnimation
//            if (!aiResponseState.isTopic) {
            OcrLoadingAnimation(progress = aiResponseState.progress)
//            }
        }

        is AiResponseState.Loading -> {
            // 加载中：显示 AiLoadingAnimation
            AiLoadingAnimation()
        }

        is AiResponseState.ContentOcrFailed -> {
            // 内容 OCR 失败：显示 RetakePlaceHolder 并提供重新拍摄功能
            RetakePlaceHolder(
                modifier = Modifier.fillMaxSize(),
                retake = retake ?: {},
                errorMessage = aiResponseState.errorMessage
                    ?: "作文识别失败，请上传清晰、完整的图片哦～"
            )
        }

        is AiResponseState.TitleOcrFailed -> {
            // 标题 OCR 失败：未做具体处理
            RetakePlaceHolder(
                modifier = Modifier.fillMaxSize(),
                retake = retake ?: {},
                errorMessage = aiResponseState.errorMessage
                    ?: "题目识别失败，请上传清晰、完整的图片哦～"
            )
        }


        else -> {
            // 其他状态：显示 ComeOnPlaceHolder
//            ComeOnPlaceHolder()
            AiLoadingAnimation()
        }
    }
}


/**
 * 识别成功
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AiSuccessLayout(
    sectorList: List<AiSector>,
    selectedSector: AiSector,
    sectorJobStatus: JobStatus,
    scoreJobStatus: JobStatus,
    subSectors: List<AiSubSector>,
    score: Float,
    totalScore: Int,
    switchAiTab: (AiSector) -> Unit,
    footer: (@Composable () -> Unit)? = null,
    bottomBar: @Composable () -> Unit,
    allusionList: List<CompositionCheckBean.Allusion>?,
    adviceList: List<CompositionCheckBean.Advice>?,
    comprehensiveJudge: CompositionCheckBean.ComprehensiveJudge?,
    allSuccess: Boolean,
    sentenceClickIndex: Int = -1,
    aiStatus: String? = "first",
    initFeedback: EvaluateFeedbackState? = null,
    feedbackClick: ((AiSector, EvaluateFeedback, Boolean) -> Unit)? = null,
    source: String? = ""
) {
    // 使用 Scaffold 提供底部操作栏
    Scaffold(
        bottomBar = bottomBar
    ) { innerPadding ->
        // 主要内容区域，使用 Box 包裹以实现居中布局
        val baseModifier = Modifier
            .fillMaxSize()
            .padding(innerPadding) // padding 应该应用在最外层或背景之后，取决于你的意图

        // 根据 source 条件化地添加背景
        val finalModifier = if (source == "popupView") {
            baseModifier.background(
                color = Color.White, // 使用纯色背景
                shape = RoundedCornerShape(15.dp)
            )
        } else {
            baseModifier.background(
                brush = Brush.linearGradient(
                    colors = listOf(Color(0xFFFAFCFF), Color(0xFFEFF8FF)),
                    start = Offset(0f, 0f),
                    end = Offset(0f, Float.POSITIVE_INFINITY)
                ),
                shape = RoundedCornerShape(15.dp),
                alpha = 0.85f // brush 版本的 background 支持 alpha
            )
        }

        Box(
            modifier = finalModifier
        ) {
            // 内容容器，使用 Column 实现垂直布局
            Column(
                modifier = Modifier
                    .wrapContentSize()
                    .padding(horizontal = 0.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 如果 sectorList 不为空，则显示选项卡布局；否则显示一个占位 Spacer
                if (sectorList.isNotEmpty() && aiStatus != "second") {
                    CompositionAiTabLayout(
                        selectedSector = selectedSector,
                        onClick = switchAiTab,
                        sectorList = sectorList
                    )
                } else {
                    Spacer(modifier = Modifier.size(20.dp))
                }

                // 根据 allSuccess 状态决定展示 AiMergeList 还是 AiSectorList
                if (allSuccess) {
                    val mergeList = sectorList.map { aiSector ->
                        aiSector.getAiSubSectorList(comprehensiveJudge, adviceList, allusionList)
                            .filter {
                                it.comments.isNotEmpty() || it.placeHolder != null
                            }
                    }
                    // 展示合并后的 AI 结果列表
                    AiMergeList(
                        sectorList = sectorList,
                        aiSector = selectedSector,
                        subSectorsList = mergeList,
                        footer = footer,
                        switchAiTab = switchAiTab,
                        sentenceClickIndex = sentenceClickIndex,
                        scoreJobStatus = scoreJobStatus,
                        score = score,
                        totalScore = totalScore,
                        initFeedback = initFeedback,
                        feedbackClick = feedbackClick
                    )
                } else {
                    // 展示单一的 AI 结果列表
                    AiSectorList(
                        jobStatus = sectorJobStatus,
                        aiSector = selectedSector,
                        subSectors = subSectors.filter { // 有内容或者占位符才需要显示
                            it.comments.isNotEmpty() || it.placeHolder != null
                        },
                        footer = footer,
                        scoreJobStatus = scoreJobStatus,
                        score = score,
                        totalScore = totalScore,
                    )
                }
            }
        }
    }
}

/**
 * 综合评价中的分数
 */
@Composable
private fun ScoreLayout(score: Float, totalScore: Int) {
    Row(
        modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            text = "预估",
            color = Color(0xFF17233D),
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold
        )
        if (score >= 0) {
            Text(
                text = "$score",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFFEB5848)
            )
        } else {
            Text(
                text = "生成中...", fontSize = 18.sp, color = Color(0xFF99A3FF)
            )
        }
        if (totalScore > 0) {
            Text(
                text = "(满分$totalScore)", fontSize = 18.sp, color = Color(0xFF515A6E)
            )
        } else {
            Text(
                text = "(满分15)", fontSize = 18.sp, color = Color(0xFF515A6E)
            )
        }

    }
}

@Composable
fun AiMergeList(
    sectorList: List<AiSector>,
    aiSector: AiSector,
    subSectorsList: List<List<AiSubSector>>,
    footer: (@Composable () -> Unit)? = null,
    switchAiTab: (AiSector) -> Unit,
    sentenceClickIndex: Int = -1,
    scoreJobStatus: JobStatus? = null,
    score: Float = -1f,
    totalScore: Int = -1,
    initFeedback: EvaluateFeedbackState? = null,
    feedbackClick: ((AiSector, EvaluateFeedback, Boolean) -> Unit)? = null,
) {
    // 如果 subSectorsList 为空，则显示占位符
    if (subSectorsList.isEmpty()) {
        Box(
            modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center
        ) {
            aiSector.placeHolder?.invoke()
        }
        return
    }
    // 创建 LazyListState 用于管理滚动状态
    val listState = rememberLazyListState()
    // 获取各个子部分的大小
    val firstSize = subSectorsList.firstOrNull()?.size ?: 0
    val secondSize = subSectorsList.getOrNull(1)?.size ?: 0
    val thirdSize = subSectorsList.getOrNull(2)?.size ?: 0
    // 定义各个子部分的索引范围
    val firstRange = Pair(0, (subSectorsList.firstOrNull()?.size ?: 0))
    val secondRange =
        Pair(
            firstRange.second + 1,
            firstRange.second + (subSectorsList.getOrNull(1)?.size ?: 0)
        )
    val thirdRange =
        Pair(
            secondRange.second + 1,
            secondRange.second + (subSectorsList.getOrNull(2)?.size ?: 0)
        )
    // 监听 aiSector 变化并重置滚动位置
    LaunchedEffect(key1 = aiSector) {
        val aiSectorIndex = sectorList.indexOf(aiSector)
        when {
            aiSectorIndex == 0 && listState.firstVisibleItemIndex !in firstRange.first until firstRange.second -> {
                listState.scrollToItem(0) // 使用偏移量
            }

            aiSectorIndex == 1 && listState.firstVisibleItemIndex !in secondRange.first until secondRange.second -> {
                listState.scrollToItem(secondRange.first) // 使用偏移量
            }

            aiSectorIndex == 2 && listState.firstVisibleItemIndex !in thirdRange.first until thirdRange.second -> {
                listState.scrollToItem(thirdRange.first) // 使用偏移量
            }
        }
    }
    // 监听 sentenceClickIndex 并滚动到指定位置
    LaunchedEffect(key1 = sentenceClickIndex) {
        if (sentenceClickIndex >= 0) {
            listState.scrollToItem(firstSize + secondSize)
        }
    }
    var lastSector = 0
    // 滚动监听：获取当前可视区域的第一个 item 索引
    LaunchedEffect(Unit) {
        snapshotFlow { listState.firstVisibleItemIndex }.collect { index ->
            // 找到该 index 对应的 AiSubSector
            val visibleItems = listState.layoutInfo.visibleItemsInfo
            if (visibleItems.isEmpty()) {
                return@collect
            }
            val sectorToSwitch: AiSector? = when (index) {
                in firstRange.first..firstRange.second -> {
                    sectorList.getOrNull(0)
                }

                in secondRange.first..secondRange.second -> {
                    sectorList.getOrNull(1)
                }

                else -> {
                    sectorList.getOrNull(2)
                }
            }
            // 防止频繁调用
            if (sectorToSwitch != null && sectorList.indexOf(sectorToSwitch) != lastSector) {
                switchAiTab(sectorToSwitch)
                lastSector = sectorList.indexOf(sectorToSwitch)
            }
        }
    }
    // 使用 LazyColumn 实现垂直滚动列表
    LazyColumn(
        state = listState,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 在 LazyColumn 内部添加 ScoreLayout

        var scoreResult = -1f
        var totalScoreResult = -1
        if (scoreJobStatus == JobStatus.SUCCESS && scoreJobStatus != null) {
            scoreResult = score
            totalScoreResult = totalScore
        }

        // 遍历 subSectorsList 并为每个子部分创建 itemsIndexed
        subSectorsList.forEachIndexed { i, subSectors ->
            if (i == 1 && subSectors.isNotEmpty()) {
                item {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.Start
                    ) {
                        Image(
                            modifier = Modifier.size(width = 166.dp, height = 36.dp),
                            painter = painterResource(id = R.drawable.icon_zsjyz_title),
                            contentDescription = null
                        )
                    }
                }
            }
            if (subSectors.isNotEmpty()) {
                itemsIndexed(subSectors) { index, item ->
                    // 显示 AiSubSectorLayout
                    AiSubSectorLayout(
                        aiSubSector = item, backgroundColor = item.backgroundColor
                    ) {
                        if (i == 0 && index == 0) ScoreLayout(
                            scoreResult,
                            totalScoreResult
                        ) else null
                    }

                }
            }
            // 在每个 AiSubSectorLayout 后面添加 CompositionPraiseWidget
            if (subSectors.isNotEmpty()) {
                item {
                    Box(
                        modifier = Modifier.padding(start = 24.dp)
                    ) {
                        CompositionPraiseWidget(
                            selectedAiSector = sectorList[i],
                            initFeedback = initFeedback?.feedbackMap?.get(sectorList[i]),
                            needAnimation = initFeedback?.needAnimation ?: false,
                            feedbackClick = feedbackClick
                        )
                    }
                }
            }
        }
        // 如果 footer 不为空，则添加 footer 到列表末尾
        if (footer != null) {
            item {
                footer()
            }
        }
        // 添加一个无内容的 Spacer 作为结尾
        item {
            Spacer(modifier = Modifier.size(0.dp))
        }
    }
}

@Composable
fun AiSectorList(
    jobStatus: JobStatus,
    aiSector: AiSector,
    subSectors: List<AiSubSector>,
    footer: (@Composable () -> Unit)? = null,
    scoreJobStatus: JobStatus? = null,
    score: Float = -1f,
    totalScore: Int = -1
) {
    // 如果是加油站且正在运行，则显示加载动画
    if (jobStatus.isRunning() && aiSector is AiSector.Petrol) { // 仅加油站
        AiLoadingAnimation()
        return
    }
    // 如果 subSectors 为空，则显示占位符
    if (subSectors.isEmpty()) {
        Box(
            modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center
        ) {
            aiSector.placeHolder?.invoke()
        }
        return
    }
    // 创建 LazyListState 用于管理滚动状态
    val listState = rememberLazyListState()
    // 监听数据变化并重置滚动位置
    LaunchedEffect(key1 = aiSector) {
        listState.scrollToItem(0) // 使用偏移量
    }
    // 使用 LazyColumn 实现垂直滚动列表
    LazyColumn(
        state = listState,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 在 LazyColumn 内部添加 ScoreLayout

        var scoreResult = -1f
        var totalScoreResult = -1
        if (scoreJobStatus == JobStatus.SUCCESS) {
            scoreResult = score
            totalScoreResult = totalScore
        }
        // 遍历 subSectors 并为每个子部分创建 itemsIndexed
        itemsIndexed(subSectors) { _, item ->
            when (aiSector) {
                AiSector.Evaluate -> { // 综合评价
                    AiSubSectorLayout(
                        aiSubSector = item,
                        scoreLayout = { ScoreLayout(scoreResult, totalScoreResult) }
                    )
                }

                AiSector.Dibble -> { // 点拨
                    AiSubSectorLayout(
                        aiSubSector = item,
                    )
                }

                AiSector.Petrol -> { // 加油站
                    AiSubSectorLayout(
                        aiSubSector = item,
                        backgroundColor = Color(0xFFF5F6FD),
                    )
                }

                is AiSector.OnePage -> { // 一稿
                }
            }
        }
        // 如果 footer 不为空，则添加 footer 到列表末尾
        if (footer != null) {
            item {
                footer()
            }
        }
        // 添加一个无内容的 Spacer 作为结尾
        item {
            Spacer(modifier = Modifier.size(0.dp))
        }
    }
}

// ----------------------------------------------------------------------
@Composable
fun CompositionAiTabLayout(
    selectedSector: AiSector,
    onClick: (AiSector) -> Unit,
    sectorList: List<AiSector>,
) {
    val actualSelectedSector = if (sectorList.any { it.id == selectedSector.id }) {
        selectedSector // 期望选中的在列表中，就用它
    } else {
        sectorList.first() // 期望选中的不在列表中，默认选中第一个
    }
    LaunchedEffect(actualSelectedSector, selectedSector, sectorList) {
        // 只有在列表非空，且实际选中的不是外部期望选中的那个（通常是因为回退）
        // 并且实际选中的确实是列表的第一个（确保我们是因为回退到第一个才触发）
        // 或者更简单：只要 actualSelectedSector 和 selectedSector 的 id 不同，就同步
        if (sectorList.isNotEmpty() && actualSelectedSector.id != selectedSector.id) {
            onClick(actualSelectedSector) // "自动触发" click，将实际选中的Tab同步回ViewModel
        }
    }
    Row(
        modifier = Modifier.height(74.dp),
        horizontalArrangement = Arrangement.spacedBy(28.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        sectorList.forEach {
            CompositionSelectedIcon(
                modifier = Modifier
                    .width(147.dp)
                    .height(40.dp),
                normalRes = it.normalRes,
                selectedRes = it.selectedRes,
                isSelected = actualSelectedSector.id == it.id,
                onClick = {
                    onClick(it)
                })
        }
    }
}

// ----------------------------------------------------------------------
// ----------------------------------------------------------------------
@Composable
private fun AiSubSectorLayout(
    aiSubSector: AiSubSector,
    backgroundColor: Color? = null,
    scoreLayout: (@Composable () -> Unit)? = null,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 24.dp),
    ) {
        Spacer(modifier = Modifier.size(20.dp))
        if (aiSubSector.iconRes != null) {
            Image(
                modifier = Modifier,
                painter = painterResource(aiSubSector.iconRes),
                contentDescription = null
            )
            Spacer(modifier = Modifier.size(15.dp))
        }
        when {
            aiSubSector.comments.isEmpty() -> {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(208.dp)
                        .background(
                            color = backgroundColor ?: aiSubSector.backgroundColor,
                            shape = RoundedCornerShape(CORNER_RADIUS)
                        )
                        .padding(20.dp), contentAlignment = Alignment.Center
                ) {
                    aiSubSector.placeHolder?.invoke()
                }
            }

            aiSubSector is AiSubSector.Sentence -> { // 句子特殊处理
                aiSubSector.comments.forEachIndexed { index, aiSectorComment ->
                    if (index > 0) {
                        Spacer(modifier = Modifier.size(15.dp))
                    }
                    // 不需要使用 AiSectorComment的 color
                    AiSubSectorCommentItem(
                        content = "${aiSectorComment.content}\n${aiSectorComment.comment}",
                        backgroundColor = backgroundColor ?: aiSubSector.backgroundColor,
                    )
                }
            }

            else -> {
                aiSubSector.comments.forEachIndexed { index, aiSectorComment ->
                    if (index > 0) {
                        Spacer(modifier = Modifier.size(15.dp))
                    }

                    if (scoreLayout != null) {
                        AiSubSectorCommentItem(
                            content = aiSectorComment.comment,
                            backgroundColor = backgroundColor ?: aiSubSector.backgroundColor,
                            scoreLayout = {
                                scoreLayout()
                            }
                        )
                    } else {
                        // 不需要使用 AiSectorComment的 color
                        AiSubSectorCommentItem(
                            content = aiSectorComment.comment,
                            backgroundColor = backgroundColor ?: aiSubSector.backgroundColor,
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun AiSubSectorCommentItem(
    content: String?,
    backgroundColor: Color,
    scoreLayout: (@Composable () -> Unit)? = null,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = backgroundColor, shape = RoundedCornerShape(CORNER_RADIUS)
            )
            .padding(20.dp)
    ) {
        // 显示 scoreLayout（如果存在）
        if (scoreLayout != null) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 12.dp) // 给 scoreLayout 和内容之间留出间距
            ) {
                scoreLayout()
            }
        }

        Row(
            modifier = Modifier.fillMaxHeight()
        ) {
            AiCommentContent(
                content = content,
            )
        }
    }
}


//@Composable
//fun AiCommentContentWithIndication(
//    indicator: Int,
//    indicatorColor: Color,
//    title: String,
//    content: String?,
//) {
//    ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
//        val (indicatorView, titleView, contentView) = createRefs()
//        CircleIndicatorIcon(
//            modifier = Modifier
//                .width(22.dp)
//                .height(22.dp)
//                .constrainAs(indicatorView) {
//                    top.linkTo(parent.top)
//                    start.linkTo(parent.start)
//                },
//            index = indicator,
//            indicatorColor = indicatorColor,
//        )
//        Text(
//            modifier = Modifier
//                .constrainAs(titleView) {
//                    top.linkTo(indicatorView.top)
//                    bottom.linkTo(indicatorView.bottom)
//                    start.linkTo(indicatorView.end, margin = 8.dp)
//                },
//            text = title,
//            fontSize = CompositionFonts.AiTitleFontSize,
//            fontWeight = FontWeight.Bold,
//            color = colorResource(R.color.text_black_color)
//        )
//        Spacer(modifier = Modifier.size(8.dp))
//        AiCommentContent(
//            modifier = Modifier
//                .constrainAs(contentView) {
//                    top.linkTo(titleView.bottom, margin = 8.dp)
//                    start.linkTo(anchor = titleView.start)
//                    end.linkTo(anchor = parent.end)
//                    width = Dimension.fillToConstraints
//                },
//            content = content
//        )
//    }
//}

@Composable
fun AiCommentContent(
    modifier: Modifier = Modifier,
    content: String?,
) {
    if (LocalInspectionMode.current) {
        // Preview 模式下显示占位符
        Text(
            text = content ?: "",
            modifier = modifier,
            fontSize = CompositionFonts.AiCommonFontSize,
            color = Color(0xFF17233D)
        )
        return
    }
    Box(modifier) {
        AndroidView(
            modifier = Modifier
                .wrapContentSize()
                .heightIn(
                    max = 16000.dp
                ), factory = { context ->
                MarkDownCommentView(context).apply {
                    id = R.id.ai_comment_tv
                    setTextColor(android.graphics.Color.BLACK)
                    setPadding(0, 0, 0, 0)
                    setEditMode(false)
                }
            }, update = { view ->
                view.setPreviewTextContent(content ?: "")
            })
    }
}


@Preview
@Composable
fun AiSectorListPreview() {
    Box(modifier = Modifier.background(color = Color.White)) {
        AiSectorList(
            jobStatus = JobStatus.SUCCESS,
            aiSector = AiSector.Petrol,
            subSectors = CompositionMockData.mockAiSubSectorList()
        )
    }
}