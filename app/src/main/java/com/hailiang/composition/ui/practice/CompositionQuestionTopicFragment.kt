package com.hailiang.composition.ui.practice

import android.annotation.SuppressLint
import android.app.Activity.RESULT_OK
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.core.view.isEmpty
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.hailiang.common.base.BaseFragment
import com.hailiang.composition.ui.photograph.PhotoViewModel
import com.hailiang.composition.ui.photograph.TakePhotoCompositionActivity
import com.hailiang.composition.ui.widget.CORNER_RADIUS
import com.hailiang.composition.ui.widget.CompositionQuestionTopicLayout
import com.hailiang.core.ext.launchAndCollect
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.hlutil.dp
import com.hailiang.hlutil.dpInt
import com.hailiang.hlutil.ext.clipOutline
import com.hailiang.hlutil.id
import com.hailiang.xxb.en.composition.R


/**
 * Description:  查看题目
 *
 * <AUTHOR>
 * @version 2025/2/21 10:58
 */
class CompositionQuestionTopicFragment : BaseFragment(R.layout.fragment_composition_check_question) {
    private val topicImagesLayout: LinearLayout by id(R.id.ll_question_topic)
    private val compositionAiContentContainer: FrameLayout by id(R.id.composition_ai_content_container)
    private val compositionViewModel by activityViewModels<CompositionViewModel>()

    private val takePhotoLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            HLog.i(HTag.TAG, "takePhotoLauncher result = $result")
            if (result.resultCode == RESULT_OK) {
                result.data?.let { intent ->
                    val WORK_ID = "workId"
                    val WORK_STATE_ID = "workStateId"
                    val workId = intent.getLongExtra(WORK_ID, 0)
                    val workStateId = intent.getLongExtra(WORK_STATE_ID, 0)
                    val requireActivity = requireActivity()
                    CompositionGuidanceActivity.start(
                        requireActivity(), workId, workStateId
                    )
                    requireActivity.finish()
                }
            }
        }


    @SuppressLint("MissingInflatedId")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        compositionViewModel.compositionContentState.launchAndCollect(viewLifecycleOwner) {
            showQuestionContent(it)
        }
        compositionAiContentContainer.removeAllViews()
        compositionAiContentContainer.addView(
            ComposeView(requireActivity()).apply {
                setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
                setContent {
                    val aiCorrectState by compositionViewModel.aiResponseState.collectAsStateWithLifecycle()
                    // 判断是否需要显示白色背景
                    val shouldShowBackground = when (aiCorrectState) {
                        is AiResponseState.Default -> true
                        is AiResponseState.OcrLoading -> if ((aiCorrectState as AiResponseState.OcrLoading).isTopic) true else false
                        is AiResponseState.TitleOcrFailed -> true
                        else -> false
                    }

                    Column(
                        modifier = Modifier.fillMaxSize().clip(RoundedCornerShape(CORNER_RADIUS))
                            .wrapContentSize(Alignment.Center).let {
                                if (shouldShowBackground) it.background(Color.White) else it
                            }) {
                        CompositionQuestionTopicLayout(aiCorrectState, retakePhotoAction = {
                            val activity = requireActivity()
                            takePhotoLauncher.launch(
                                TakePhotoCompositionActivity.createIntent(
                                    activity,
                                    compositionViewModel.curWorkId,
                                    compositionViewModel.curWorkStateId,
                                    PhotoViewModel.TIME_FIRST,
                                    PhotoViewModel.TYPE_TITLE
                                )
                            )
                        })
                    }
                }
            })
    }

    private fun showQuestionContent(contentState: CompositionContentState) {
        val taskList = contentState.workDetail?.taskList
        if (taskList == null) {
            return
        }
        if (!topicImagesLayout.isEmpty()) {
            return
        }
        topicImagesLayout.post {
            val context = context
            if (context == null) return@post
            for (taskBean in taskList) {
                taskBean.taskMaterialList?.forEach { materialBean ->
                    HLog.i(TAG, "load image: ${materialBean.materialFileUrl}")
                    val imageView = ImageView(context)
                    imageView.clipOutline(15.dp)
                    Glide.with(context).asBitmap().load(materialBean.materialFileUrl)
                        .into(object : SimpleTarget<Bitmap>() {
                            override fun onResourceReady(
                                resource: Bitmap, transition: Transition<in Bitmap>?
                            ) {
                                HLog.i(TAG, "load image success: ${materialBean.materialFileUrl}")
                                // 设置宽度为父布局的宽度
                                val width = topicImagesLayout.width
                                // 按比例计算高度
                                val height = (width * 1f / resource.width * resource.height).toInt()

                                val layoutParams = LinearLayout.LayoutParams(
                                    LinearLayout.LayoutParams.MATCH_PARENT, // 宽度撑满
                                    height
                                )
                                layoutParams.topMargin = 2.dpInt
                                imageView.layoutParams = layoutParams
                                imageView.setImageBitmap(resource)
                            }

                            override fun onLoadFailed(errorDrawable: Drawable?) {
                                super.onLoadFailed(errorDrawable)
                                HLog.e(TAG, "load image failed: ${materialBean.materialFileUrl}")
                            }
                        })
                    topicImagesLayout.addView(imageView)
                }
            }
        }
    }
}