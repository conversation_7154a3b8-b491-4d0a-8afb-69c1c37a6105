package com.hailiang.composition.ui.practice

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.hailiang.common.compose.theme.BackIcon
import com.hailiang.composition.ui.practice.theme.CompositionBrush
import com.hailiang.composition.ui.practice.theme.CompositionFonts
import com.hailiang.composition.ui.widget.CompositionStepLayout
import com.hailiang.composition.ui.widget.FragmentInCompose
import com.hailiang.hlutil.HLog

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/5/23 18:49
 */
@Composable
fun CompositionScreen(
    selectedStep: CompositionStep,
    onTabClicked: (CompositionStep) -> Unit,
    onBackPressed: () -> Unit,
    isShowKeyBoard: Boolean = true,
    aiProcessingState: AiProcessingState? = null
) {
    val allSteps = if (selectedStep.step < CompositionStep.SubmitPractice.step) {
        listOf(
            CompositionStep.FirstPractice,
            CompositionStep.AiGuidance,
            CompositionStep.SubmitPractice,
        )
    } else {
        listOf(
            CompositionStep.CheckQuestion,
            CompositionStep.CheckFirstPractice,
            CompositionStep.CheckSecondPractice,
        )
    }

    CompositionCheckScreen(
        selectedStep = selectedStep,
        allSteps = allSteps,
        onTabClicked = onTabClicked,
        onBackPressed = onBackPressed,
        isShowKeyBoard = isShowKeyBoard,
        aiProcessingState = aiProcessingState
    )
}


@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun CompositionCheckScreen(
    selectedStep: CompositionStep,
    allSteps: List<CompositionStep>,
    onTabClicked: (CompositionStep) -> Unit,
    onBackPressed: () -> Unit,
    isShowKeyBoard: Boolean = true,
    aiProcessingState: AiProcessingState? = null
) {
    Scaffold(
        modifier = Modifier.background(
            brush = CompositionBrush.screenBackgroundBrush
        ),
        containerColor = Color.Transparent,
        topBar = {
            if (isShowKeyBoard) {
                CompositionCheckTopBar(
                    title = "作文批改",
                    selectedStep = selectedStep,
                    allSteps = allSteps,
                    onTabClicked = onTabClicked,
                    onBackPressed = onBackPressed,
                )
            }
        }
    ) { innerPadding ->
        var fragment by remember { mutableStateOf(selectedStep.fragment()) }
        LaunchedEffect(selectedStep) {
            fragment = selectedStep.fragment()
        }
        Box {
            if (selectedStep.showAiStatus && aiProcessingState != null) {
                // 右上角新增提示组件
                when (aiProcessingState) {
                    is AiProcessingState.Idle -> {

                    }

                    is AiProcessingState.Success -> {

                    }

                    else -> {
                        HLog.i("CompositionCheckScreen", "aiProcessingState: $aiProcessingState")
                        CompositionRightBottomStatus(
                            aiProcessingState = aiProcessingState,
                            modifier = Modifier
                                .align(Alignment.TopEnd)
                                .offset(y = (-22).dp, x = 60.dp)
                        )
                    }
                }
            }
            Box(
                modifier = Modifier
                    .padding(innerPadding)

            ) {
                fragment?.let {
                    FragmentInCompose(it)
                }
            }
        }

    }
}

/**
 * 做题的TopBar
 */
@Composable
internal fun CompositionCheckTopBar(
    title: String,
    selectedStep: CompositionStep,
    allSteps: List<CompositionStep>,
    onTabClicked: (CompositionStep) -> Unit,
    onBackPressed: () -> Unit,
) {
    Row(
        modifier = Modifier
            .padding(top = 20.dp)
            .fillMaxWidth()
            .height(56.dp),
        horizontalArrangement = Arrangement.Start,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.width(20.dp))
        BackIcon(onBackPressed)
        Spacer(modifier = Modifier.width(12.dp))
        Text(
            text = title,
            maxLines = 1,
            fontSize = CompositionFonts.TitleFontSize,
            fontWeight = FontWeight.Bold,
            overflow = TextOverflow.Ellipsis
        )
        Spacer(modifier = Modifier.width(160.dp))
        CompositionStepLayout(
            modifier = Modifier,
            selectedStep = selectedStep,
            activatedStep = selectedStep.activatedStep,
            allSteps = allSteps,
            onTabClicked = onTabClicked
        )
        Spacer(modifier = Modifier.width(50.dp))
    }
}


@Preview(apiLevel = 33, widthDp = 800)
@Composable
private fun CompositionCheckScreenPreview() {
    CompositionCheckScreen(
        selectedStep = CompositionStep.FirstPractice,
        allSteps = listOf(
            CompositionStep.FirstPractice,
            CompositionStep.AiGuidance,
            CompositionStep.SubmitPractice,
        ),
        onTabClicked = {},
        onBackPressed = {}
    )
}