package com.hailiang.composition.ui.widget

import android.graphics.BitmapFactory
import androidx.compose.foundation.Image
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.hailiang.composition.ui.practice.AiResponseState
import com.hailiang.composition.ui.practice.AiSecondResponseState
import com.hailiang.composition.ui.practice.CompositionQuestionImageState
import com.hailiang.composition.ui.practice.CompositionStudentAnswersBitmapState
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.xxb.en.composition.R

private val STUDENT_IMAGE_CORNER_RADIUS = 12.dp

@Composable
fun CompositionStudentImage(
    aiResponseState: AiResponseState,
    bitmapState: CompositionStudentAnswersBitmapState,
    questionsBitmap: CompositionQuestionImageState? = null,
    imageModifier: Modifier = Modifier.padding(10.dp),
) {
    val verticalScrollState = rememberScrollState() // 在这里调用
    HLog.i(HTag.TAG, "CompositionStudentImage questionsBitmap = $questionsBitmap")
    RenderStudentImage(
        aiResponseState = aiResponseState,
        bitmapState = bitmapState,
        questionsBitmap = questionsBitmap,
        verticalScrollState = verticalScrollState, // 传递 ScrollState
        imageModifier = imageModifier
    )
}

@Composable
fun CompositionStudentImage(
    aiResponseState: AiSecondResponseState,
    bitmapState: CompositionStudentAnswersBitmapState,
    questionsBitmap: CompositionQuestionImageState? = null,
    imageModifier: Modifier = Modifier.padding(10.dp),
) {
    val verticalScrollState = rememberScrollState() // 在这里调用
    RenderStudentImage(
        aiResponseState = aiResponseState,
        bitmapState = bitmapState,
        questionsBitmap = questionsBitmap,
        verticalScrollState = verticalScrollState, // 传递 ScrollState
        imageModifier = imageModifier
    )
}

@Composable
private fun RenderStudentImage(
    aiResponseState: Any,
    bitmapState: CompositionStudentAnswersBitmapState,
    questionsBitmap: CompositionQuestionImageState? = null,
    verticalScrollState: ScrollState, // 修正为 ScrollState 类型
    imageModifier: Modifier = Modifier.padding(10.dp),
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .clip(RoundedCornerShape(STUDENT_IMAGE_CORNER_RADIUS))
            .background(color = Color.White),
        contentAlignment = Alignment.TopCenter
    ) {
        when (bitmapState) {
            is CompositionStudentAnswersBitmapState.Loading -> {
                LoadingAnimation()
            }

            is CompositionStudentAnswersBitmapState.Success -> {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .verticalScroll(state = verticalScrollState) // 使用传入的 ScrollState
                        .then(imageModifier)
                ) {
                    // 学生作答图片（bitmapState）
                    bitmapState.combineImage?.asImageBitmap()?.let { bitmap ->
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clip(RoundedCornerShape(STUDENT_IMAGE_CORNER_RADIUS))
                                .then(
                                    when (aiResponseState) {
                                        is AiResponseState.ContentOcrFailed,
                                        is AiSecondResponseState.SecondOcrFailed -> Modifier.border(
                                            1.dp,
                                            Color(0xFFEE0019),
                                            RoundedCornerShape(STUDENT_IMAGE_CORNER_RADIUS)
                                        )

                                        else -> Modifier
                                    }
                                )
                        ) {
                            Image(
                                modifier = Modifier.fillMaxWidth(),
                                bitmap = bitmap,
                                contentDescription = null,
                                contentScale = ContentScale.FillWidth
                            )
                        }
                    }

                    // 题目图片（questionsBitmap）
                    if (questionsBitmap != null && questionsBitmap is CompositionQuestionImageState.Success) {
                        questionsBitmap.combineImage?.asImageBitmap()?.let { bitmap ->
                            Spacer(Modifier.height(8.dp))
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clip(RoundedCornerShape(STUDENT_IMAGE_CORNER_RADIUS))
                                    .then(
                                        if (aiResponseState is AiResponseState.TitleOcrFailed ||
                                            aiResponseState is AiSecondResponseState.SecondOcrFailed
                                        ) {
                                            Modifier.border(
                                                1.dp,
                                                Color(0xFFEE0019),
                                                RoundedCornerShape(STUDENT_IMAGE_CORNER_RADIUS)
                                            )
                                        } else {
                                            Modifier
                                        }
                                    )
                            ) {
                                Image(
                                    modifier = Modifier.fillMaxWidth(),
                                    bitmap = bitmap,
                                    contentDescription = null,
                                    contentScale = ContentScale.FillWidth
                                )
                            }
                        }
                    }
                }

                // ScannerAnimation 覆盖在图片上
                when (aiResponseState) {
                    is AiResponseState.OcrLoading,
                    is AiSecondResponseState.SecondOcrLoading -> {
                        ScannerAnimation(modifier = Modifier.fillMaxSize())
                    }

                    else -> {}
                }
            }
        }
    }
}

@Composable
@Preview(widthDp = 800)
private fun CompositionStudentImagePreview() {
    val context = LocalContext.current
    CompositionStudentImage(
        aiResponseState = AiResponseState.Loading,
        bitmapState = CompositionStudentAnswersBitmapState.Success(
            BitmapFactory.decodeResource(context.resources, R.drawable.ic_image)
        )
    )
}