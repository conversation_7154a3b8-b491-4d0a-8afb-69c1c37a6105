package com.hailiang.composition.ui.widget

import android.graphics.BitmapFactory
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import com.hailiang.composition.ui.practice.AiResponseState
import com.hailiang.composition.ui.practice.CompositionQuestionImageState
import com.hailiang.composition.ui.practice.CompositionStudentAnswersBitmapState
import com.hailiang.composition.ui.practice.CompositionTableState
import com.hailiang.question.en.composition.EnComposition
import com.hailiang.xxb.en.composition.R


@Composable
fun CompositionStudentLeftWidget(
    aiResponseState: AiResponseState,
    bitmapState: CompositionStudentAnswersBitmapState,
    compositionTableState: CompositionTableState,
    correctedComposition: EnComposition?,
    questionsBitmap: CompositionQuestionImageState? = null,
    lowerAlpha: Float = 1f,
) {
    when (aiResponseState) {
        is AiResponseState.OcrLoading,
        is AiResponseState.TitleOcrFailed,
        is AiResponseState.ContentOcrFailed,
            -> { // 仅OCR 时显示动画
            CompositionStudentImage(
                aiResponseState = aiResponseState,
                bitmapState = bitmapState,
                questionsBitmap = questionsBitmap
            )
        }

        else -> {
            when (compositionTableState) {
                is CompositionTableState.Loading -> {
                    CompositionStudentImage(
                        aiResponseState = aiResponseState,
                        bitmapState = bitmapState,
                        questionsBitmap = questionsBitmap
                    )
                }

                is CompositionTableState.Success -> {
                    CompositionEnTableWidget(
                        composition = correctedComposition ?: compositionTableState.composition,
                        aiResponseState = aiResponseState,
                        lowerAlpha = lowerAlpha,
                    )
                }
            }
        }
    }
}

@Composable
@Preview(
    widthDp = 800,
)
private fun CompositionStudentLeftWidgetPreview() {
    val context = LocalContext.current
    val exampleContent =
        """On March 7,1907, the English statistician Francis Galton published a paper which illustrated what has come to be known as the “wisdom of crowds” effect. The experiment of estimation he conducted showed that in some cases, the average of a large number of independent estimates could be quite accurate. 
This effect capitalizes on the fact that when people make errors, those errors aren’t always the same. Some people will tend to overestimate, and some to underestimate. When enough of these errors are averaged together, they cancel each other out, resulting in a more accurate estimate. If people are similar and tend to make the same errors, then their errors won’t cancel each other out. In more technical terms, the wisdom of crowds requires that people’s estimates be independent. If for whatever reasons, people’s errors become correlated or dependent, the accuracy of the estimate will go down.
But a new study led by Joaquin Navajas offered an interesting twist on this classic phenomenon. The key finding of the study was that when crowds were further divided into smaller groups that were allowed to have a discussion, the averages from these groups were more accurate than those from an equal number of independent individuals. For instance, the average obtained from the estimates of four discussion groups of five was significantly more accurate than the average obtained from 20 independent individuals. 
In a follow-up study with 100 university students, the researchers tried to get a better sense of what the group members actually did in their discussion. Did they tend to go with those most confident about their estimates? Did they follow those least willing to change their minds? This happened some of the time, but it wasn’t the dominant response. Most frequently, the groups reported that they “shared arguments and reasoned together”. Somehow, these arguments and reasoning resulted in a global reduction in error. Although the studies led by Navajas have limitations and many questions remain, the potential implications for group discussion and decision-making are enormous.""".trimIndent()

    CompositionStudentLeftWidget(
        aiResponseState = AiResponseState.Loading,
        bitmapState = CompositionStudentAnswersBitmapState.Success(
            BitmapFactory.decodeResource(
                context.resources, R.drawable.ic_image
            )
        ),
        compositionTableState = CompositionTableState.Loading,
        null
    )
}

fun main() {
    val a =
        "For example, Lisa is more outgoing than me and she exercises every day while I only exercises three times a week."
    System.out.println(a.get(92))

    System.out.println(a.length.toString())
}