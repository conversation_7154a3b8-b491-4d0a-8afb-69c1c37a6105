package com.hailiang.composition.ui

import android.os.Bundle
import android.widget.Button
import android.widget.EditText

import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope

import com.hailiang.core.ext.setSingleClickListener
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag

import com.hailiang.hlutil.id
import com.hailiang.symspellcorrect.CorrectManager

import com.hailiang.xxb.en.composition.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

import kotlin.getValue

class TestActivity : AppCompatActivity() {

    private val editTextText: EditText by id(R.id.editTextText)
    private val button: Button by id(R.id.button)


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_test)
        //初始化
        lifecycleScope.launch(Dispatchers.Default) {
            CorrectManager.loadModel(this@TestActivity) //加载模型
        }
        button.setSingleClickListener {
            //测试
            HLog.i(
                HTag.TAG,
                "singleWordCorrection ${CorrectManager.processTextWithCorrections(editTextText.text.toString())}"
            )
        }
//        val exampleContent =
//            """On March 7,1907, the English statistician Francis Galton published a paper which illustrated what has come to be known as the “wisdom of crowds” effect. The experiment of estimation he conducted showed that in some cases, the average of a large number of independent estimates could be quite accurate.
//This effect capitalizes on the fact that when people make errors, those errors aren’t always the same. Some people will tend to overestimate, and some to underestimate. When enough of these errors are averaged together, they cancel each other out, resulting in a more accurate estimate. If people are similar and tend to make the same errors, then their errors won’t cancel each other out. In more technical terms, the wisdom of crowds requires that people’s estimates be independent. If for whatever reasons, people’s errors become correlated or dependent, the accuracy of the estimate will go down.
//But a new study led by Joaquin Navajas offered an interesting twist on this classic phenomenon. The key finding of the study was that when crowds were further divided into smaller groups that were allowed to have a discussion, the averages from these groups were more accurate than those from an equal number of independent individuals. For instance, the average obtained from the estimates of four discussion groups of five was significantly more accurate than the average obtained from 20 independent individuals.
//In a follow-up study with 100 university students, the researchers tried to get a better sense of what the group members actually did in their discussion. Did they tend to go with those most confident about their estimates? Did they follow those least willing to change their minds? This happened some of the time, but it wasn’t the dominant response. Most frequently, the groups reported that they “shared arguments and reasoned together”. Somehow, these arguments and reasoning resulted in a global reduction in error. Although the studies led by Navajas have limitations and many questions remain, the potential implications for group discussion and decision-making are enormous.""".trimIndent()
        val exampleContent = "aaaa"
        editTextText.setText(exampleContent)

    }
}

fun main() {
    val a = "On March 7,1907, the English statistician Francis "
    System.out.printf(a.length.toString())
}