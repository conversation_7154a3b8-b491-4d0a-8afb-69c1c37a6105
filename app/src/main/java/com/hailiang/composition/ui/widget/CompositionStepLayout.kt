package com.hailiang.composition.ui.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.core.graphics.toColorInt
import com.hailiang.common.compose.theme.AppColors
import com.hailiang.composition.ui.practice.CompositionStep
import com.hailiang.xxb.en.composition.R

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/2/20 21:15
 */


// ----------------------------------------------------------------------
@Composable
fun CompositionStepLayout(
    modifier: Modifier,
    selectedStep: CompositionStep,
    activatedStep: CompositionStep,
    allSteps: List<CompositionStep>,
    onTabClicked: (CompositionStep) -> Unit,
) {
    // 使用remember缓存计算结果
    val activatedIndex = remember(activatedStep) {
        allSteps.indexOfFirst { it.step == activatedStep.step }
    }
    val stepBarShape = RoundedCornerShape(28.dp)
    val stepBarHeight = 44.dp
    Row(
        modifier = modifier
            .background(
                color = Color("#BCD2F2".toColorInt()),
                shape = stepBarShape
            )
            .height(stepBarHeight)
    ) {
        Row(
            modifier = Modifier
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            Color("#DEEFFF".toColorInt()),
                            Color("#F7FBFF".toColorInt())
                        )
                    ),
                    shape = stepBarShape
                )
                .height(stepBarHeight),
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            val selectedStepIndex = selectedStep.step
            val activatedStepIndex = activatedStep.step
            var index = 0
            allSteps.subList(0, activatedIndex + 1).forEach { item ->
                Spacer(modifier = Modifier.size(20.dp))
                if (index > 0) {
                    Image(
                        modifier = Modifier
                            .height(14.dp)
                            .align(Alignment.CenterVertically),
                        painter = painterResource(
                            if (index <= activatedStepIndex) {
                                R.drawable.ic_arrow_ai_step_selected
                            } else {
                                R.drawable.ic_arrow_ai_step_unselected
                            }
                        ),
                        contentDescription = null
                    )
                }
                CompositionStepItem(
                    itemStep = item,
                    selected = selectedStepIndex == item.step,
                    activated = activatedStepIndex >= item.step,
                    onTabClicked = onTabClicked
                )
                index++
            }
            Spacer(modifier = Modifier.size(20.dp))
        }
//        如果取了3个 这个就不能有了
        if (activatedIndex + 1 < allSteps.size) {
            Row(
                modifier = Modifier,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                val selectedStepIndex = selectedStep.step
                val activatedStepIndex = activatedStep.step
                val index = 2
                allSteps.subList(activatedIndex + 1, allSteps.size).forEach { item ->
                    Spacer(modifier = Modifier.size(10.dp))
                    if (index > 0) {
                        Image(
                            modifier = Modifier
                                .height(14.dp)
                                .align(Alignment.CenterVertically),
                            painter = painterResource(
                                if (item.step <= activatedStepIndex) {
                                    R.drawable.ic_arrow_ai_step_selected
                                } else {
                                    R.drawable.ic_arrow_ai_step_unselected
                                }
                            ),
                            contentDescription = null
                        )
                    }
                    CompositionStepItem(
                        itemStep = item,
                        selected = selectedStepIndex == item.step,
                        activated = activatedStepIndex >= item.step,
                        onTabClicked = onTabClicked
                    )
                }
                Spacer(modifier = Modifier.size(20.dp))
            }
        }

    }
}

@Composable
private fun CompositionStepItem(
    itemStep: CompositionStep,
    selected: Boolean,
    activated: Boolean,
    onTabClicked: (CompositionStep) -> Unit,
) {
    ConstraintLayout(
        modifier = Modifier
            .height(45.dp)
            .clickable(
                enabled = activated && itemStep.fragment() != null,
                indication = null,
                interactionSource = remember { MutableInteractionSource() },
                onClick = {
                    onTabClicked.invoke(itemStep)
                }
            )
    ) {
        val (textIcon, indicator, background) = createRefs()
        Image(
            modifier = Modifier
                .height(20.dp)
                .padding(horizontal = 18.dp)
                .constrainAs(textIcon) {
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                    start.linkTo(parent.start)
                },
            painter = painterResource(itemStep.stepIcon),
            contentScale = ContentScale.FillHeight,
            contentDescription = null,
            colorFilter = ColorFilter.tint(
                if (selected) AppColors.TextBlue else if (activated) AppColors.TextBlack else colorResource(
                    com.hailiang.xxb.resource.R.color.black_opaque_40
                )
            )
        )

    }
}

// ----------------------------------------------------------------------

@Composable
@Preview(widthDp = 800)
private fun CompositionToolbarPreview() {
    Column(modifier = Modifier.background(Color(0xffffffff))) {
        CompositionStepLayout(
            modifier = Modifier,
            selectedStep = CompositionStep.CheckFirstPractice,
            activatedStep = CompositionStep.CheckFirstPractice,
            allSteps = listOf(
                CompositionStep.CheckQuestion,
                CompositionStep.CheckFirstPractice,
                CompositionStep.CheckSecondPractice,
            ),
            onTabClicked = {}
        )

        CompositionStepLayout(
            modifier = Modifier,
            selectedStep = CompositionStep.FirstPractice,
            activatedStep = CompositionStep.AiGuidance,
            allSteps = listOf(
                CompositionStep.FirstPractice,
                CompositionStep.AiGuidance,
                CompositionStep.SecondPractice,
            ),
            onTabClicked = {}
        )
        CompositionStepLayout(
            modifier = Modifier,
            selectedStep = CompositionStep.CheckQuestion,
            activatedStep = CompositionStep.CheckSecondPractice,
            allSteps = listOf(
                CompositionStep.CheckQuestion,
                CompositionStep.CheckFirstPractice,
                CompositionStep.CheckSecondPractice,
            ),
            onTabClicked = {}
        )
    }
}