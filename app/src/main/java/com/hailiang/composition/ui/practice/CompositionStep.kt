package com.hailiang.composition.ui.practice

import androidx.fragment.app.Fragment
import com.hailiang.xxb.en.composition.R

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/2/21 11:14
 */
sealed class CompositionStep(
    val name: String,
    val step: Int,
    val stepIcon: Int,
    val fragment: () -> Fragment?,
    val showAiStatus: Boolean = false,
) {
    var activatedStep: CompositionStep = this

    data object Default : CompositionStep(
        name = "", step = 0, stepIcon = R.drawable.step_first_answer, fragment = { null })

    //题目详情
    data object FirstPractice : CompositionStep(
        name = "首次作答",
        step = 1,
        stepIcon = R.drawable.step_topic_detail,
        fragment = {
            CompositionQuestionTopicFragment()
        },
    ) {
        var editable: Boolean = true
    }

    //一流
    data object AiGuidance : CompositionStep(
        name = "一稿点拨、改写",
        step = 2,
        stepIcon = R.drawable.step_first_answer,
        fragment = { CompositionAiGuidanceFragment() },
        showAiStatus = true
    )

    //一流后的作答
    data object SecondPractice : CompositionStep(
        name = "二次作答",
        step = 2,
        stepIcon = R.drawable.step_first_answer,
        fragment = { CompositionSecondPracticeFragment() },
    )

    //二稿批改
    data object SubmitPractice : CompositionStep(
        name = "提交",
        step = 4,
        stepIcon = R.drawable.step_second_answer,
        fragment = { null }
    )

    // ----------------------------------------------------------------------
    //二流
    data object CheckQuestion : CompositionStep(
        name = "上传题目",
        step = 5,
        stepIcon = R.drawable.step_topic_detail,
        fragment = { CompositionQuestionTopicFragment() }
    )

    data object CheckFirstPractice : CompositionStep(
        name = "一稿点拨、改写",
        step = 6,
        stepIcon = R.drawable.step_first_answer,
        fragment = { CompositionCheckFirstFragment() }
    )

    data object CheckSecondPractice : CompositionStep(
        name = "二稿批改",
        step = 7,
        stepIcon = R.drawable.step_second_answer,
        fragment = { CompositionCheckSecondFragment() },
        showAiStatus = true
    )
}