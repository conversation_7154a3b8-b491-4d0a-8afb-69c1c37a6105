package com.hailiang.composition.ui.widget

import android.app.Activity
import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.hailiang.composition.data.enums.JobStatus
import com.hailiang.composition.ui.practice.AiProcessingState
import com.hailiang.composition.ui.practice.AiResponseState
import com.hailiang.composition.ui.practice.AiSector
import com.hailiang.composition.ui.practice.AiSectorComment
import com.hailiang.composition.ui.practice.AiSubSector
import com.hailiang.composition.ui.practice.CompositionStep
import com.hailiang.composition.ui.practice.CompositionTableState
import com.hailiang.composition.ui.practice.CompositionViewModel
import com.hailiang.question.en.composition.EnComposition
import com.hailiang.question.en.composition.InputMethodHelper
import com.hailiang.xxb.en.composition.R

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/3/17 21:27
 */
class CompositionAiResultWindow(val context: Context) {
    fun show(viewModel: CompositionViewModel) {
        dismiss()
        val view = (context as Activity).window.decorView as ViewGroup
        val popupView = ComposeView(context).apply {
            this.layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            id = R.id.ai_result_window
            setContent {
                val aiResponseState by viewModel.aiResponseState.collectAsStateWithLifecycle()
                val aiProcessingState by viewModel.aiProcessingState.collectAsStateWithLifecycle()
                val compositionTableState by viewModel.compositionTableState.collectAsStateWithLifecycle()
                val compositionCorrectTableState by viewModel.compositionCorrectTableState.collectAsStateWithLifecycle()
                Box(
                    modifier = Modifier
                        .padding(top = 6.dp, start = 24.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .size(width = 606.dp, height = 374.dp)
                    ) {
                        AiResultPopupView(
                            aiResponseState = aiResponseState,
                            switchAiTab = { sector ->
                                viewModel.switchAiTab(
                                    compositionStep = CompositionStep.SecondPractice,
                                    aiSector = sector
                                )
                            },
                            back = {
                                viewModel.toAiGuidance()
                                InputMethodHelper.dismiss(context)

                            },
                            reload = viewModel::reloadAiStream,
                            footer = {},
                            aiProcessingState = aiProcessingState,
                            compositionTableState = compositionTableState,
                            correctedComposition = compositionCorrectTableState
                        )
                    }
                }
            }
        }
        view.addView(popupView)
    }

    fun dismiss() {
        val view = (context as Activity).window.decorView as ViewGroup
        view.findViewById<View>(R.id.ai_result_window)?.let {
            view.removeView(it)
        }
    }
}

// ----------------------------------------------------------------------
@Composable
private fun AiResultPopupView(

    aiResponseState: AiResponseState,
    switchAiTab: (AiSector) -> Unit,
    footer: @Composable () -> Unit,
    reload: () -> Unit,
    back: () -> Unit,
    aiProcessingState: AiProcessingState? = null,
    compositionTableState: CompositionTableState? = null,
    correctedComposition: EnComposition? = null,
) {
    Column(
        modifier = Modifier
            .background(
                color = Color.White,
                shape = RoundedCornerShape(CORNER_RADIUS)
            )
            .padding(horizontal = 20.dp, vertical = 2.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 6.dp),
            contentAlignment = Alignment.CenterEnd
        ) {
            RestoreBackIcon(
                modifier = Modifier,
                back = back
            )
        }
        AiResultLayout(
            aiResponseState = aiResponseState,
            aiTabList = listOf(
                AiSector.OnePage(correctedComposition, compositionTableState),
                AiSector.Dibble,
                AiSector.Petrol,
            ),
            switchAiTab = switchAiTab,
            retry = {},
            reload = reload,
            bottomBar = footer,
            aiProcessingState = aiProcessingState,
            source = "popupView"
        )
    }

}


@Preview(
    widthDp = 800
)
@Composable
fun CompositionDiffWindowPreview() {
    AiResultPopupView(
        aiResponseState = AiResponseState.Success(
            allJobStatus = JobStatus.SUCCESS,
            scoreJobStatus = JobStatus.SUCCESS,
            allusionJobStatus = JobStatus.SUCCESS,
            selectedAiSector = AiSector.Dibble, aiSubSectorList = listOf(
                AiSubSector.Advantage(
                    comments = listOf(
                        AiSectorComment.Advantage(
                            comment = "11111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111" + "1111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111"
                        ),
                    )
                ),
                AiSubSector.Suggestion(
                    comments = listOf(
                        AiSectorComment.Suggestion(comment = "Suggestion")
                    )
                ),
            ), comprehensiveJudge = null, adviceList = null, allusionList = null, score = 18f,
            totalScore = 60
        ),
        switchAiTab = {},
        reload = {},
        footer = {},
        back = {},
    )
}
