package com.hailiang.composition.ui.practice.theme

import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/3/21 12:01
 */
object CompositionBrush {
    /**
     * 议论文，整体背景
     * 0.0 ～ 1.0。左上 到 右下
     */
    val screenBackgroundBrush = Brush.linearGradient(
        0.0f to Color(0xFFC5E5FD),
        1.0f to Color(0xFFCCDFFF),
    )

    val aiBtnBrush = Brush.linearGradient(
        0.0f to Color(0xFFBF9DF5),
        1.0f to Color(0xFF3968FF),
    )

}