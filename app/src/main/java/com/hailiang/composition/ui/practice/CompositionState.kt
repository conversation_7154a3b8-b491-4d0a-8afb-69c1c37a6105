package com.hailiang.composition.ui.practice

import android.graphics.Bitmap
import androidx.annotation.IntRange
import com.hailiang.common.download.resource.WorkResource
import com.hailiang.composition.data.bean.AiStreamDetail
import com.hailiang.composition.data.bean.AiStreamType
import com.hailiang.composition.data.bean.CompositionCheckBean.Advice
import com.hailiang.composition.data.bean.CompositionCheckBean.Allusion
import com.hailiang.composition.data.bean.CompositionCheckBean.ComprehensiveJudge
import com.hailiang.composition.data.bean.CompositionStatusResponse
import com.hailiang.composition.data.bean.WorkDetail
import com.hailiang.composition.data.enums.JobStatus
import com.hailiang.composition.mediatools.MediaImage
import com.hailiang.question.en.composition.EnComposition
import java.util.UUID

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/3/16 02:23
 */
/**
 * 作业题目、材料等信息
 */
data class CompositionContentState(
    val workId: Long = -1L,
    val workStateId: Long = -1L,
    val workDetail: WorkDetail? = null,
)

///**
// * 学生答案图片资源
// */
data class CompositionStudentAnswersState(
    val firstAnswerList: List<String>? = null,
    val imageResources: List<WorkResource>? = null,
    val mediaImageList: List<MediaImage>? = null,
)


/**
 * 学生答案图片Bitmap
 */
sealed interface CompositionStudentAnswersBitmapState {
    data class Success(
        val combineImage: Bitmap?,
    ) : CompositionStudentAnswersBitmapState

    data object Loading : CompositionStudentAnswersBitmapState
}

/**
 * 题目图片Bitmap
 */
sealed interface CompositionQuestionImageState {
    data class Success(
        val combineImage: Bitmap?,
    ) : CompositionQuestionImageState

    data object Loading : CompositionQuestionImageState
}

/**
 *  学生答案图片和题目图片的状态
 */
sealed class CombinedImagesBitmapState {
    // 加载中状态（可细分）
    object Loading : CombinedImagesBitmapState()

    // 成功状态（携带两种图片）
    data class Success(
        val studentImage: Bitmap?,
        val questionImage: Bitmap?
    ) : CombinedImagesBitmapState()

    // 部分成功状态
    sealed class Partial : CombinedImagesBitmapState() {
        data class StudentOnly(val bitmap: Bitmap) : Partial()
        data class QuestionOnly(val bitmap: Bitmap) : Partial()
    }

    // 错误状态
    sealed class Error : CombinedImagesBitmapState() {
        data class StudentFailed(val exception: Throwable) : Error()
        data class QuestionFailed(val exception: Throwable) : Error()
        data class AllFailed(val exceptions: List<Throwable>) : Error()
    }
}

/**
 * 作文表格状态
 */
sealed class CompositionTableState {
    data object Loading : CompositionTableState() {}

    data class Success(
        val ocrTitle: String?,
        val ocrContent: String?,
        val composition: EnComposition?,
        val secondComposition: EnComposition? = null,
        val needDoCompare: Boolean,
    ) : CompositionTableState()
}

sealed interface AiSecondResponseState {

    data object Default : AiSecondResponseState

    data object SecondOcrFailed : AiSecondResponseState


    data object SecondOcrLoading : AiSecondResponseState


    data object SecondOcrSuccess : AiSecondResponseState
}

/**
 * Ai 批改状态
 */
sealed interface AiResponseState {
    data object Default : AiResponseState

    data object Loading : AiResponseState // request loading

    data class OcrLoading(@IntRange(0, 100) val progress: Int, val isTopic: Boolean) :
        AiResponseState

    /**
     * 流准备中
     */
    data class AiStreamPreparing(
        val streamType: AiStreamType,
        val preAiStreamDetail: AiStreamDetail?,
    ) : AiResponseState

    /**
     * 思考、回答中
     */
    data class AiStreaming(val aiStreamDetail: AiStreamDetail, val streamType: AiStreamType) :
        AiResponseState

    data object AiFirstCorrect : AiResponseState

    data object AiSecondCorrect : AiResponseState

    /**
     *  流失败，要重试，触发服务端重试
     */
    data class AiStreamError(val aiStreamDetail: AiStreamDetail) : AiResponseState

    /**
     * 请求失败，要重新请求
     */
    data class AiRequestError(val aiStreamDetail: AiStreamDetail) : AiResponseState

    /**
     * 标题OCR失败
     */
    data class TitleOcrFailed(
        val id: String = UUID.randomUUID().toString(),
        val compositionStatusResponse: CompositionStatusResponse?,
        val errorMessage: String?,
    ) : AiResponseState

    /**
     * 内容OCR 失败
     */
    data class ContentOcrFailed(
        val id: String = UUID.randomUUID().toString(),
        val errorMessage: String?,
    ) : AiResponseState

    /**
     * AI重试
     * 会调用重试接口，触发服务端重试
     */
    data object Retry : AiResponseState

    /**
     * 重新加载，直接调用接口重新获取数据，不触发服务端重试
     */
    data object Reload : AiResponseState

    data class Success(
        val allJobStatus: JobStatus,
        val scoreJobStatus: JobStatus = JobStatus.SUCCESS,
        val allusionJobStatus: JobStatus = JobStatus.SUCCESS,
        val secondComJudgeStatus: JobStatus = JobStatus.SUCCESS,
        val selectedAiSector: AiSector,
        val aiSubSectorList: List<AiSubSector>,
        /**
         * 综合评价pp
         */
        val comprehensiveJudge: ComprehensiveJudge?,
        /**
         * 点拨
         */
        val adviceList: List<Advice>?,
        /**
         * 典故列表（知识加油站）
         */
        val allusionList: List<Allusion>?,
        val score: Float,
        val totalScore: Int,
    ) : AiResponseState {
        val selectedJobStatus
            get() = when (selectedAiSector) {
                AiSector.Evaluate -> allJobStatus
                AiSector.Dibble -> allJobStatus
                AiSector.Petrol -> allusionJobStatus
                else -> allJobStatus
            }
    }
}

data class EvaluateFeedbackState(
    val feedbackMap: Map<AiSector, EvaluateFeedback?>,
    val needAnimation: Boolean,
)