package com.hailiang.composition.ui.practice

import androidx.compose.runtime.Immutable
import com.hailiang.composition.data.bean.AiStreamType

@Immutable
sealed interface AiProcessingState {

    data object Idle : AiProcessingState // 空闲
    data object RecognizingContent : AiProcessingState // 作文识别中
    data object ContentRecognitionFailed : AiProcessingState // 作文识别失败
    data object GrammarChecking : AiProcessingState // 正在纠错
    data object EssayGrading : AiProcessingState // 作文正在定档中
    data object FeedbackGenerating : AiProcessingState // 正在生成点拨
    data object ServiceError : AiProcessingState // 服务异常
    data object Success : AiProcessingState // 成功完成
    data object NetworkError : AiProcessingState // 网络异常

    companion object {
        fun obtainFromAiResponseState(aiState: AiResponseState): AiProcessingState {
            return when (aiState) {
                is AiResponseState.OcrLoading -> {
                    if (!aiState.isTopic) {
                        // 作文识别中
                        RecognizingContent
                    } else {
                        Idle
                    }
                }

                is AiResponseState.ContentOcrFailed -> {
                    // 作文识别失败
                    ContentRecognitionFailed
                }

                is AiResponseState.AiStreaming -> {
                    if(aiState.aiStreamDetail.isNetworkError()) {
                        return NetworkError
                    }
                    if(aiState.aiStreamDetail.isStreamError()){
                       return ServiceError
                    }
                    if (aiState.aiStreamDetail.isCompletion()) {
                        return Idle
                    }
                    // 流输出中
                    when (aiState.streamType) {
                        AiStreamType.FirstCorrection, AiStreamType.SecondCorrection -> {
                            // 正在纠错
                            GrammarChecking
                        }
                        // 正在生成点拨
                        AiStreamType.First, AiStreamType.Second -> {
                            FeedbackGenerating
                        }

                        else -> {
                            Idle
                        }
                    }
                }

                is AiResponseState.AiStreamPreparing -> {
                    if (aiState.streamType == AiStreamType.First || aiState.streamType == AiStreamType.Second) {
                        // 一流、二流定档
                        EssayGrading
                    } else {
                        Idle
                    }
                }

                is AiResponseState.AiStreamError, is AiResponseState.AiRequestError, is AiResponseState.Reload, is AiResponseState.Retry -> {
                    ServiceError
                }

                is AiResponseState.Success -> {
                    Success
                }

                else -> {
                    Idle
                }
            }
        }
    }

}
