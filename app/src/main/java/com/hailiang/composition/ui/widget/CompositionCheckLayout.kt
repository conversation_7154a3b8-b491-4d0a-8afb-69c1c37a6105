package com.hailiang.composition.ui.widget

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.hailiang.composition.data.enums.JobStatus
import com.hailiang.composition.ui.practice.AiProcessingState
import com.hailiang.composition.ui.practice.AiResponseState
import com.hailiang.composition.ui.practice.AiSector
import com.hailiang.composition.ui.practice.CompositionAiProcess
import com.hailiang.composition.ui.practice.CompositionCheckViewModel
import com.hailiang.composition.ui.practice.CompositionStep
import com.hailiang.composition.ui.practice.CompositionViewModel
import com.hailiang.composition.ui.practice.EvaluateFeedback
import com.hailiang.composition.ui.practice.EvaluateFeedbackState

/**
 * 查看首次作答
 */
@Composable
fun CompositionCheckFirstLayout(
    aiResponseState: AiResponseState,
    studentFeedbackState: EvaluateFeedbackState,
    switchAiTab: (AiSector) -> Unit,
    retry: () -> Unit,
    reload: () -> Unit,
    feedbackClick: (AiSector, EvaluateFeedback, Boolean) -> Unit,
    aiProcessingState: AiProcessingState? = null,
) {
    AiResultLayout(
        aiResponseState = aiResponseState,
        aiTabList = listOf(
            AiSector.Dibble,
            AiSector.Petrol,
        ),
        switchAiTab = switchAiTab,
        retry = retry,
        reload = reload,
        bottomBar = {
//            CompositionPraiseWidget(
//                aiResponseState = aiResponseState,
//                initFeedback = studentFeedbackState,
//                feedbackClick = feedbackClick,
//            )
        },
        aiProcessingState = aiProcessingState,
        initFeedback = studentFeedbackState,
        feedbackClick = feedbackClick
    )
}

/**
 * 查看二次作答
 */
@Composable
fun CompositionCheckSecondLayout(
    compositionViewModel: CompositionViewModel,
    checkViewModel: CompositionCheckViewModel,
) {
    val aiResponseState by compositionViewModel.aiResponseState.collectAsStateWithLifecycle()
    val feedbackState by compositionViewModel.studentSecondFeedbackState.collectAsStateWithLifecycle()
    val sentenceClickIndex by compositionViewModel.sentenceClickIndex.collectAsStateWithLifecycle()
    val aiProcessingState by compositionViewModel.aiProcessingState.collectAsStateWithLifecycle()
    val selectedStep by compositionViewModel.compositionStepState.collectAsStateWithLifecycle()
    Column {
        if (selectedStep.showAiStatus) {
            when (aiProcessingState) {
                is AiProcessingState.GrammarChecking, is AiProcessingState.FeedbackGenerating, is
                AiProcessingState.EssayGrading -> {
                    CompositionAiProcess(
                        aiProcessingState = aiProcessingState,
                        modifier = Modifier.padding(start = 20.dp, end = 20.dp, top = 20.dp)
                    )
                }


                else -> {
//                    CompositionAiProcess(
//                        aiProcessingState = aiProcessingState,
//                        modifier = Modifier.padding(start = 20.dp, end = 20.dp, top = 20.dp)
//                    )
                }
            }
        }
        AiResultLayout(
            aiResponseState = aiResponseState,
            aiTabList = listOf(
//            AiSector.Evaluate,
                AiSector.Dibble,
                AiSector.Petrol,
            ),
            switchAiTab = {
                compositionViewModel.switchAiTab(
                    compositionStep = CompositionStep.CheckSecondPractice,
                    aiSector = it
                )
            },
            retry = compositionViewModel::retryAiStream,
            reload = compositionViewModel::reloadAiStream,
            retake = {},
            bottomBar = {
//            CompositionPraiseWidget(
//                aiResponseState = aiResponseState,
//                initFeedback = feedbackState,
//                feedbackClick = { aiSector, evaluateFeedback, selected ->
//                    compositionViewModel.secondDraftFeedback(
//                        aiSector,
//                        evaluateFeedback,
//                        selected
//                    )
//                },
//            )
            },
            sentenceClickIndex = sentenceClickIndex,
            aiProcessingState = aiProcessingState,
            aiStatus = "second",
            initFeedback = feedbackState,
            feedbackClick = { aiSector, evaluateFeedback, selected ->
                compositionViewModel.secondDraftFeedback(
                    aiSector,
                    evaluateFeedback,
                    selected
                )
            },
        )
    }

}

//@Composable
//@Preview(widthDp = 900, heightDp = 2000)
//private fun CompositionCheckSecondPreview() {
//    CompositionCheckSecondLayout(
//        checkState = CompositionCheckViewModel.TeacherCheckState.Success(
//            teacherChecked = true,
//            selectedAiSector = AiSector.Dibble,
//            aiSubSectorList = CompositionMockData.mockAiSubSectorList(),
//            preScore = 10,
//            score = 15,
//        ),
//        aiTabList = listOf(
//            AiSector.Evaluate,
//            AiSector.Dibble,
//        ),
//        switchAiTab = { },
//        retry = { },
//        reload = {}
//    )
//}

@Composable
@Preview(
    widthDp = 900,
    heightDp = 2000,
)
private fun CompositionCheckFirstPreview() {
    CompositionCheckFirstLayout(
        aiResponseState = AiResponseState.Success(
            allJobStatus = JobStatus.SUCCESS,
            scoreJobStatus = JobStatus.SUCCESS,
            allusionJobStatus = JobStatus.SUCCESS,
            selectedAiSector = AiSector.Dibble,
            aiSubSectorList = CompositionMockData.mockAiSubSectorList(),
            comprehensiveJudge = null,
            adviceList = null,
            allusionList = null,
            score = 18f,
            totalScore = 60
        ),
        studentFeedbackState = EvaluateFeedbackState(
            feedbackMap = emptyMap(),
            needAnimation = false
        ),
        switchAiTab = { },
        retry = { },
        reload = {},
        feedbackClick = { _, _, _ -> }
    )
}
