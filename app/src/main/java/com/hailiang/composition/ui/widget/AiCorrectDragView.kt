package com.hailiang.composition.ui.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.hailiang.composition.data.enums.JobStatus
import com.hailiang.composition.ui.practice.AiProcessingState
import com.hailiang.composition.ui.practice.AiResponseState
import com.hailiang.composition.ui.practice.AiSector
import com.hailiang.composition.ui.practice.AiSectorComment
import com.hailiang.composition.ui.practice.AiSubSector
import com.hailiang.composition.ui.practice.CompositionStudentAnswersBitmapState
import com.hailiang.composition.ui.practice.CompositionTableState
import com.hailiang.composition.ui.practice.EvaluateFeedback
import com.hailiang.composition.ui.practice.EvaluateFeedbackState
import com.hailiang.question.en.composition.EnComposition

@Composable
fun AiCorrectDragView(
    aiResponseState: AiResponseState,
    bitmapState: CompositionStudentAnswersBitmapState,
    compositionTableState: CompositionTableState,
    correctedComposition: EnComposition?,
    defaultHeight: Float,
    refreshHeight: (Float) -> Unit,
    switchAiTab: (AiSector) -> Unit,
    onBack: () -> Unit,
    reload: () -> Unit,
    footer: @Composable () -> Unit,
    enable: Boolean,
    initFeedback: EvaluateFeedbackState? = null,
    feedbackClick: ((AiSector, EvaluateFeedback, Boolean) -> Unit)? = null,
    isShowKeyboardHidden: Boolean? = true,
) {
    if (isShowKeyboardHidden == false) {
        CompositionStudentLeftWidget(
            aiResponseState = aiResponseState,
            bitmapState = bitmapState,
            compositionTableState = compositionTableState,
            correctedComposition = correctedComposition,
            lowerAlpha = 0.8f,
        )
    } else {
        SplitScreenLayout(
            topLayout = {
                CompositionStudentLeftWidget(
                    aiResponseState = aiResponseState,
                    bitmapState = bitmapState,
                    compositionTableState = compositionTableState,
                    correctedComposition = correctedComposition,
                    lowerAlpha = 0.8f
                )
            },
            bottomLayout = {
                AiCorrectDragContent(
                    aiResponseState = aiResponseState,
                    switchAiTab = switchAiTab,
                    footer = footer,
                    reload = reload,
                    initFeedback = initFeedback,
                    feedbackClick = feedbackClick
                )
            },
            dragLayout = {
                Shadow(0.dp)
                Shadow(1.dp)
                Shadow(2.dp)
                Shadow(3.dp)
                Shadow(4.dp)
                Shadow(5.dp)
                AiCorrectDragHeader(
                    enable = enable,
                    back = onBack
                )
            },
            defaultTopHeight = defaultHeight,
            dragHeight = 56.dp,
            minTopHeight = 120.dp,
            minBottomHeight = 80.dp,
            heightChanged = {
                refreshHeight(it)
            })
    }


}

@Composable
private fun Shadow(padding: Dp) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(padding)
            .background(
//                color = Color(0x05000000),
                brush = Brush.linearGradient(
                    colorStops = arrayOf(
                        0.0f to Color(0x01000000),
                        1.0f to Color(0x29000000),
                    ), start = Offset(0F, 0F), end = Offset(0f, Float.POSITIVE_INFINITY)
                ),
                shape = RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp),
            )
            .blur(radiusX = 6.dp, radiusY = 6.dp)
    )
}

@Composable
fun AiCorrectDragHeader(enable: Boolean, back: () -> Unit) {
    Box(
        modifier = Modifier
            .height(46.dp)
            .fillMaxWidth()
            .clip(RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp))
            .background(color = Color.White)
            .padding(horizontal = 16.dp),
    ) {
        Box(
            modifier = Modifier
                .width(76.dp)
                .height(8.dp)
                .background(color = Color(0xFFC6C6C6), shape = RoundedCornerShape(4.dp))
                .align(Alignment.Center)
        )
        RestoreBackIcon(
            modifier = Modifier.align(Alignment.CenterEnd),
            enable = enable,
            back = back
        )
    }
}

@Composable
fun AiCorrectDragContent(
    aiResponseState: AiResponseState,
    switchAiTab: (AiSector) -> Unit,
    footer: @Composable () -> Unit,
    reload: () -> Unit,
    aiProcessingState: AiProcessingState? = null,
    initFeedback: EvaluateFeedbackState? = null,
    feedbackClick: ((AiSector, EvaluateFeedback, Boolean) -> Unit)? = null,
) {
    AiResultLayout(
        aiResponseState = aiResponseState,
        aiTabList = listOf(
            AiSector.Dibble,
            AiSector.Petrol,
        ),
        switchAiTab = switchAiTab,
        retry = {},
        reload = reload,
//        footer = footer,
        bottomBar = footer,
        aiProcessingState = aiProcessingState,
        initFeedback = initFeedback,
        feedbackClick = feedbackClick
    )
}

@Preview(
//    heightDp = 370
)
@Composable
private fun AiCorrectDragPreview() {
    Box(
        modifier = Modifier.background(color = Color.Red)
    ) {
        AiCorrectDragView(
            aiResponseState = AiResponseState.Success(
                allJobStatus = JobStatus.SUCCESS,
                scoreJobStatus = JobStatus.SUCCESS,
                allusionJobStatus = JobStatus.SUCCESS,
                selectedAiSector = AiSector.Dibble,
                aiSubSectorList = listOf(
                    AiSubSector.Advantage(
                        comments = listOf(
                            AiSectorComment.Advantage(
                                comment = "11111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111" + "1111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111"
                            ),
                        )
                    ),
                    AiSubSector.Suggestion(
                        comments = listOf(
                            AiSectorComment.Suggestion(comment = "Suggestion")
                        )
                    ),
                ),
                comprehensiveJudge = null,
                adviceList = null,
                allusionList = null,
                score = 18f,
                totalScore = 60

            ),
            compositionTableState = CompositionTableState.Loading,
            correctedComposition = EnComposition(
                content = "Waiting his hands almost forgotten his depression."
            ),
            bitmapState = CompositionStudentAnswersBitmapState.Loading,
            defaultHeight = 300F,
            refreshHeight = {},
            switchAiTab = {},
            onBack = {},
            reload = {},
            footer = {},
            enable = true
        )
    }
}