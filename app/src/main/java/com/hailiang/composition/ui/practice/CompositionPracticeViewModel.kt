package com.hailiang.composition.ui.practice

import android.app.Application
import android.os.SystemClock
import androidx.lifecycle.viewModelScope
import com.hailiang.composition.data.Repository
import com.hailiang.composition.data.bean.PictureAnswerInfo
import com.hailiang.composition.data.bean.WorkStatus
import com.hailiang.composition.mediatools.MediaImage
import com.hailiang.core.base.BaseAndroidViewModel
import com.hailiang.core.ext.launchOnHttp
import com.hailiang.core.ext.launchWithException
import com.hailiang.core.ext.set
import com.hailiang.core.thread.ThreadPlugins
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.hlutil.JsonUtil
import com.hailiang.hlutil.ext.jsonToList
import com.hailiang.ui.designsystem.toast.ToastUtils
import com.hailiang.view.question.composition.CompositionIndex
import com.hailiang.workcloud.data.vo.DoWorkStateInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/2/21 11:02
 */
class CompositionPracticeViewModel(application: Application) : BaseAndroidViewModel(application) {

    private val compositionRepository = Repository()
    val errorMessageState = MutableStateFlow<String?>(null)

    private var workState = DoWorkStateInfo.empty()
    private var timeCountStart: Long = Long.MAX_VALUE

    var cachedDragTopOffset = 300F

    // ----------------------------------------------------------------------
    val showSubmitTipView = MutableSharedFlow<Boolean>()

    val jumpAiGuidance = MutableSharedFlow<Boolean>()

    // ----------------------------------------------------------------------
    private var stepStartTime = -1L

    fun init() {

    }

    /**
     * 保存学生的二稿修改内容
     */
    fun saveCorrectContent(
        workId: Long,
        workStateId: Long,
        title: String?,
        content: String?,
    ) {
        viewModelScope.launch(ThreadPlugins.ioDispatcher()) {
            compositionRepository.updateCompositionPracticeInfo(
                workId = workId,
                workStateId = workStateId,
                title = title,
                content = content
            )
        }
    }

    /**
     * 二次作答提交
     */
    fun secondSubmit(
        workId: Long,
        workStateId: Long,
        canSubmit: (enable: Boolean, message: String, submitFunc: (() -> Unit)?) -> Unit,
    ) {
        viewModelScope.launchWithException(
            context = ThreadPlugins.ioDispatcher(), onError = {
                HLog.e(HTag.TAG, "preCheckSecondSubmit error", it)
                withContext(Dispatchers.Main) {
                    canSubmit(false, "提交作答发生异常: ${it.message}", null)
                }
            }) {
            val localCompositionPractice = compositionRepository.queryCompositionPracticeInfo(
                workId = workId, workStateId = workStateId
            )
            val secondPracticeContent =
                localCompositionPractice?.secondPracticeContent?.jsonToList(String::class.java)
                    ?.filter {
                        it.isNotEmpty() && it.isNotBlank()
                    }
            var enable = true
            var message = ""
            if (secondPracticeContent.isNullOrEmpty()) {
                enable = false
                message = "未作答不允许提交"
            }
            withContext(Dispatchers.Main) {
                canSubmit(
                    enable,
                    message,
                    if (enable) {
                        {
                            actualSecondSubmit(workId, workStateId)
                        }
                    } else {
                        null
                    }
                )
            }
        }
    }

    /**
     * 二次作答提交
     */
    private fun actualSecondSubmit(
        workId: Long,
        workStateId: Long,
    ) {
        showProgress()
        viewModelScope.launchOnHttp(onError = {}, doFinally = {
            hideProgress()
        }) {
            saveAndRestCounting()
            val success =
                compositionRepository.secondSubmit(workId = workId, workStateId = workStateId)
            if (success) {
                showSubmitTipView.emit(true)
                hideProgress()
            }
        }
    }

    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
//    /**
//     * 首次作答提交
//     */
//    private suspend fun firstSubmitSuccess() {
//        ToastUtils.showShort("提交成功")
//        HLog.i(HTag.TAG, "首次作答提交成功，作业状态标记为 DOWN")
//        clearErrorMessage()
//        updateWorkState(WorkStatus.DONE)
//        toAiGuidance()
//    }

    private fun showErrorMessage(errorMessage: String, needToast: Boolean = false) {
        workState.errorMessage = errorMessage
        errorMessageState.value = errorMessage
        if (needToast) {
            ToastUtils.showShort(errorMessage)
        }
    }

    private fun clearErrorMessage() {
        workState.errorMessage = null
        errorMessageState.value = null
    }

//    fun toAiGuidance() {
//        switchPracticeStep(CompositionStep.AiGuidance)
//    }

    fun requestBeginnerGuidance(toGuide: () -> Unit, toNext: () -> Unit) {
//        compositionRepository.isTakePhotoBeginnerGuidanceRead().let {
//            if (it) { // 已读，不请求
//                toNext()
//                return
//            }
//        }
//        // 未读需要请求接口，获取最新状态
//        viewModelScope.launchOnHttp {
//            val result = compositionRepository.getBeginnerGuidance()
//            if (result.isSuccessFul && result.data?.isRead() == true) {
//                compositionRepository.markTakePhotoBeginnerGuidanceRead()
//                toNext()
//                return@launchOnHttp
//            }
//            // 失败了 或者 未读，都要去引导页
//            toGuide()
//        }
    }


//    fun firstSubmit(imageUrlList: List<String>) {
//        showProgress()
//        viewModelScope.launchOnHttp(
//            doFinally = {
//                hideProgress()
//            }) {
//            clearErrorMessage()
//            //
////            val result = compositionRepository.requestJudgeJobAdd(
////                workId = curWorkId, schoolworkStateId = curWorkStateId, imageUrlList = imageUrlList
////            )
////            if (result.isSuccessFul) {
////                firstSubmitSuccess()
////            } else if (result.code == BusinessErrorCode.Submitted) {
////                firstSubmitSuccess()
////            } else {
////                ToastUtils.showShort("提交失败!")
////            }
//        }
//    }

//    fun savePictureAnswerPath(mediaImages: ArrayList<MediaImage>?) {
////        val imageResourceList = ArrayList<WorkResource>()
////        val tempPictures = ArrayList<PictureAnswerInfo>()
////        mediaImages?.forEachIndexed { index, it ->
////            it.url?.let { remoteUrl ->
////                val workResource = WorkResource.createImageResource(remoteUrl)
////                val newPath = workResource.getLocalPath(getApplication())
////                FileUtil.renameFile(it.media.path, newPath)
////                it.media.path = newPath
////                imageResourceList.add(workResource)
////            }
////            val pictureAnswerInfo = PictureAnswerInfo()
////            pictureAnswerInfo.workId = curWorkId
////            pictureAnswerInfo.path = it.media.path
////            pictureAnswerInfo.url = it.url
////            pictureAnswerInfo.index = index
////            pictureAnswerInfo.state = it.state
////            tempPictures.add(pictureAnswerInfo)
////            DBUtils.insertOrReplace(pictureAnswerInfo)
////        }
////        compositionRepository.saveTempAnswerPictures(curWorkId, tempPictures)
////        compositionRepository.updateCompositionStudentImages(
////            workId = curWorkId, imageUrls = imageResourceList.map {
////                it.url
////            })
//    }

    @Deprecated("")
    fun requestPictureAnswerInfo(): List<PictureAnswerInfo> {
//        return compositionRepository.queryTempAnswerPictures(workId = curWorkId)
//            .sortedBy { it.index }
        return listOf<PictureAnswerInfo>()
    }

    private fun updateWorkState(newState: Int) {
        workState.state = newState
//        compositionRepository.updateWorkStateInfo(workState)
    }

    fun startCounting() {
        saveAndRestCounting()
    }

    fun saveAndRestCounting() {
        val offset = SystemClock.uptimeMillis() - timeCountStart
        timeCountStart = SystemClock.uptimeMillis()
        if (offset > 0) {
            workState.timeCounting += offset
//            compositionRepository.updateWorkStateInfo(workState)
        }
    }

    fun saveDragTopOffset(topOffset: Float) {
        cachedDragTopOffset = topOffset
    }

    // ----------------------------------------------------------------------
    fun reportSwitchStepEvent() {
        if (stepStartTime > 0) {
//            BusinessBury.studentCompositionDoWorkEvent(
//                workId = curWorkId,
//                workStateId = curWorkStateId,
//                stepName = practiceStepState.value.name,
//                startTimeMillis = stepStartTime
//            )
        }
        stepStartTime = System.currentTimeMillis()
    }
}