package com.hailiang.composition.ui.practice

import android.app.Activity.RESULT_OK
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.getValue
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.lifecycleScope
import com.hailiang.common.base.BaseFragment
import com.hailiang.composition.dialog.CommonDialog
import com.hailiang.composition.ui.photograph.PhotoViewModel
import com.hailiang.composition.ui.photograph.TakePhotoCompositionActivity
import com.hailiang.composition.ui.widget.AiCorrectDragView
import com.hailiang.composition.ui.widget.CompositionAiResultWindow
import com.hailiang.composition.ui.widget.CompositionEnTableWidget
import com.hailiang.composition.ui.widget.CompositionStudentImage
import com.hailiang.composition.ui.widget.SubmitSuccessWindow
import com.hailiang.core.ext.launchAndCollect
import com.hailiang.core.ext.launchWithException
import com.hailiang.core.ext.setSingleClickListener
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.hlutil.dp
import com.hailiang.hlutil.ext.clipOutline
import com.hailiang.hlutil.ext.toJsonString
import com.hailiang.hlutil.ext.visible
import com.hailiang.question.en.composition.WordCorrect
import com.hailiang.question.en.composition.view.CompositionPaperView
import com.hailiang.symspellcorrect.CorrectManager
import com.hailiang.symspellcorrect.WordInfo
import com.hailiang.ui.designsystem.toast.ToastUtils
import com.hailiang.view.question.composition.InputMethodHelper
import com.hailiang.xxb.en.composition.R
import com.hailiang.xxb.en.composition.databinding.FragmentCompositionSecondPracticeBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.runInterruptible
import kotlinx.coroutines.withContext

/**
 * Description: 二次作答
 *
 * <AUTHOR>
 * @version 2025/2/21 10:58
 */
open class CompositionSecondPracticeFragment :
    BaseFragment(R.layout.fragment_composition_second_practice) {

    private var _binding: FragmentCompositionSecondPracticeBinding? = null
    private val binding get() = _binding!!

    private val practiceViewModel by activityViewModels<CompositionPracticeViewModel>()
    private val compositionViewModel by activityViewModels<CompositionViewModel>()

    private var correctionJob: Job? = null

    private val takePhotoLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            HLog.i(HTag.TAG, "takePhotoLauncher result = $result")
            if (result.resultCode == RESULT_OK) {
                //加载图片
                compositionViewModel.requestStudentSecondAnswerImages()
                compositionViewModel.checkSecondOcrJob()
            }

        }


    private val compositionWatcher2 = object : CompositionPaperView.TextWatcher {
        override fun onTextChanged(content: String?) {
            practiceViewModel.saveCorrectContent(
                workId = compositionViewModel.curWorkId,
                workStateId = compositionViewModel.curWorkStateId,
                title = "",
                content = listOf(content ?: "").toJsonString(),
            )
            updateTextCount(content)
            startCorrectionJob(content)
        }

        override fun onTextInitFinish(content: String?) {
            updateTextCount(content)
            startCorrectionJob(content, true)
        }
    }


    fun startCorrectionJob(
        content: String?, isInit: Boolean = false
    ) {
        correctionJob?.cancel()
        correctionJob = lifecycleScope.launch {
            if (!isInit) {
                delay(2_000L)
            }
            var lastContent = content
            withContext(Dispatchers.Default) {
                while (isActive) {
                    if (CorrectManager.isCorrectionsLoaded()) {
                        runInterruptible {
                            CorrectManager.processTextWithCorrections(lastContent ?: "")
                        }.also { result ->
                            withContext(Dispatchers.Main) {
                                HLog.i(HTag.TAG, "CorrectManager ${result}")
                                handleCorrectResult(result)
                            }
                        }
                        break // 处理完成后退出循环
                    } else {
                        // 字典未加载完成，等待2秒后重试
                        delay(2_000L)
                    }
                }
            }
        }
    }


    private fun handleCorrectResult(
        wordInfoList: List<WordInfo>
    ) {
        if (_binding == null) return
        val compositionTableLayout =
            binding.compositionStudentContentContainer.findViewById<CompositionPaperView>(
                R.id.compositionPaperView
            ) ?: run {
                HLog.e(HTag.TAG, "compositionTableLayout is null")
                return
            }
        val wordCorrectList = wordInfoList.map {
            WordCorrect(
                startPosition = it.startPosition,
                endPosition = it.endPosition,
                before = it.word,
                after = it.corrections.firstOrNull() ?: it.word,
            )
        }
        compositionTableLayout.handleCorrectResult(wordCorrectList)
    }

    private var compositionAiResultWindow: CompositionAiResultWindow? = null

    private var preImeVisible = false
    private val keyboardListener = ViewTreeObserver.OnGlobalLayoutListener {
        try {
            val imeVisible = ViewCompat.getRootWindowInsets(requireActivity().window.decorView)
                ?.isVisible(WindowInsetsCompat.Type.ime()) == true
            binding.compositionSubmitBtn.visible(!imeVisible)

            if (!imeVisible) {
                dismissAiWindow()
            }
            if (imeVisible && preImeVisible != imeVisible) {
                compositionAiResultWindow = CompositionAiResultWindow(requireContext())
                compositionAiResultWindow?.show(compositionViewModel)
            }
            preImeVisible = imeVisible
            compositionViewModel.isShowKeyBoard.value = !imeVisible
        } catch (e: Throwable) {
            //
            dismissAiWindow()
        }
    }

    private fun dismissAiWindow() {
        compositionAiResultWindow?.dismiss()
        compositionAiResultWindow = null
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = FragmentCompositionSecondPracticeBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.llCompositionStudentContentContainer.clipOutline(20.dp)
        // 加载学生图片资源
//        compositionViewModel.studentAnswerImages.launchAndCollect(viewLifecycleOwner) {
//            compositionViewModel.downloadAndCombineStudentAnswers(
//                requireActivity(),
//                it.imageResources
//            )
//        }
        //
        binding.compositionStudentImagesContainer.let {
            it.removeAllViews()
            it.addView(
                ComposeView(requireActivity()).apply {
                    setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
                    setContent {
                        val studentAnswerBitmap by compositionViewModel.studentAnswerBitmapState.collectAsStateWithLifecycle()
                        val aiResponseState by compositionViewModel.aiResponseState.collectAsStateWithLifecycle()
                        val aiSecondResponseState by compositionViewModel.aiSecondResponseState.collectAsStateWithLifecycle()

                        val studentFeedbackState by compositionViewModel.studentFeedbackState.collectAsStateWithLifecycle()
                        val compositionTableState by compositionViewModel.compositionTableState.collectAsStateWithLifecycle()
                        val compositionCorrectTableState by compositionViewModel.compositionCorrectTableState.collectAsStateWithLifecycle()
                        val isKeyboardHidden by compositionViewModel.isShowKeyBoard.collectAsStateWithLifecycle()
                        AiCorrectDragView(
                            aiResponseState = aiResponseState,
                            bitmapState = studentAnswerBitmap,
                            compositionTableState = compositionTableState,
                            correctedComposition = compositionCorrectTableState,
                            defaultHeight = practiceViewModel.cachedDragTopOffset,
                            refreshHeight = {
                                practiceViewModel.saveDragTopOffset(it)
                            },
                            switchAiTab = { sector ->
                                compositionViewModel.switchAiTab(
                                    compositionStep = CompositionStep.SecondPractice,
                                    aiSector = sector
                                )
                            },
                            onBack = {
                                dismissAiWindow()
                                compositionViewModel.reportAiTabSwitchEvent()
                                compositionViewModel.toAiGuidance()
                            },
                            reload = compositionViewModel::reloadAiStream,
                            footer = {
//                                CompositionPraiseWidget(
//                                    aiResponseState = aiResponseState,
//                                    initFeedback = studentFeedbackState,
//                                    feedbackClick = { aiSector, evaluateFeedback, selected ->
//                                        compositionViewModel.firstDraftFeedback(
//                                            aiSector, evaluateFeedback, selected
//                                        )
//                                    },
//                                )
                            },
                            initFeedback = studentFeedbackState,
                            feedbackClick = { aiSector, evaluateFeedback, selected ->
                                compositionViewModel.firstDraftFeedback(
                                    aiSector, evaluateFeedback, selected
                                )
                            },
                            enable = aiSecondResponseState !is AiSecondResponseState
                            .SecondOcrLoading,
                            isShowKeyboardHidden = isKeyboardHidden
                        )
                    }
                })
        }

        binding.btnSecondTakePhoto.setSingleClickListener {
            takePhotoLauncher.launch(
                TakePhotoCompositionActivity.createIntent(
                    requireActivity(),
                    workId = compositionViewModel.curWorkId,
                    workStateId = compositionViewModel.curWorkStateId,
                    PhotoViewModel.TIME_SECOND
                )
            )
        }
        binding.compositionSubmitBtn.setSingleClickListener { // 提交
            dismissAiWindow()
            practiceViewModel.secondSubmit(
                workId = compositionViewModel.curWorkId,
                workStateId = compositionViewModel.curWorkStateId
            ) { enable, message, submitFunc ->
                if (enable) {
                    CommonDialog.Builder().setTitle("提示")
                        .setContentText("提交后无法再修改，确定提交？")
                        .setPositiveAction(text = "提交") {
                            submitFunc?.invoke()
                        }.show(childFragmentManager)
                } else {
                    ToastUtils.showShort(message)
                }
            }
        }
        // 作文表格
        binding.compositionStudentContentContainer.let {
            it.removeAllViews()
            it.addView(ComposeView(requireActivity()).apply {
                setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
                setContent {
                    val compositionTableState by compositionViewModel.compositionTableState.collectAsStateWithLifecycle()
                    val aiResponseState by compositionViewModel.aiSecondResponseState.collectAsStateWithLifecycle()
                    val studentAnswerBitmap by compositionViewModel.studentAnswerBitmapState.collectAsStateWithLifecycle()
                    val compositionQuestionImagesBitmap by compositionViewModel.compositionQuestionImagesState.collectAsStateWithLifecycle()

                    when (aiResponseState) {
                        is AiSecondResponseState.SecondOcrLoading -> {
                            CompositionStudentImage(
                                aiResponseState = aiResponseState,
                                bitmapState =
                                    studentAnswerBitmap,
                                questionsBitmap = compositionQuestionImagesBitmap,
                            )
                        }

                        else -> {
                            val success = compositionTableState as? CompositionTableState.Success
                            CompositionEnTableWidget(
                                editable = true,
                                composition = success?.secondComposition,
                                compositionWatcher = compositionWatcher2
                            )
                        }
                    }
                }
            })
        }

        practiceViewModel.showSubmitTipView.launchAndCollect(viewLifecycleOwner) {
            compositionViewModel.markSubmitSuccess()
            val submitSuccessWindow = SubmitSuccessWindow(requireContext())
            submitSuccessWindow.show()
            compositionViewModel.reloadAiStream()
        }
        // ----------------------------------------------------------------------
        binding.root.viewTreeObserver.removeOnGlobalLayoutListener(keyboardListener)
        binding.root.viewTreeObserver.addOnGlobalLayoutListener(keyboardListener)


        compositionViewModel.aiSecondResponseState.launchAndCollect(viewLifecycleOwner) {
            when (it) {
                is AiSecondResponseState.SecondOcrLoading -> {
                    binding.btnSecondTakePhoto.isEnabled = false
                    binding.compositionSubmitBtn.isEnabled = false
                    binding.btnSecondTakePhoto.alpha = 0.4f
                    binding.compositionSubmitBtn.alpha = 0.4f
                }

                else -> {
                    binding.btnSecondTakePhoto.isEnabled = true
                    binding.compositionSubmitBtn.isEnabled = true
                    binding.btnSecondTakePhoto.alpha = 1f
                    binding.compositionSubmitBtn.alpha = 1f
                }
            }
        }

        compositionViewModel.studentSecondAnswerImages.launchAndCollect(viewLifecycleOwner) {
            compositionViewModel.downloadAndCombineStudentAnswers(it.imageResources)
        }
    }

    private fun updateTextCount(content: String?) {
        lifecycleScope.launchWithException {
            content?.let {
                val processWordSplitting = CorrectManager.processWordSplitting(it)
                binding.compositionWordCountTv.text =
                    "共${processWordSplitting.toList().size}字，已自动暂存"
                binding.compositionWordCountIv.visible(true)
                //            binding.compositionSaveTipTv.visible(true)
                binding.btnSecondTakePhoto.visible(true)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
//        ViewCompat.setOnApplyWindowInsetsListener(binding.root, null)
        binding.root.viewTreeObserver.removeOnGlobalLayoutListener(keyboardListener)
        InputMethodHelper.dismiss(requireContext())
        _binding = null
    }
}