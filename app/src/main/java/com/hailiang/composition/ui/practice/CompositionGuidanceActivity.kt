package com.hailiang.composition.ui.practice

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.airbnb.lottie.LottieCompositionFactory
import com.hailiang.common.base.BaseActivity
import com.hailiang.core.ext.launchAndCollect
import com.hailiang.hlutil.ActivityUtil

class CompositionGuidanceActivity : BaseActivity() {
    private val practiceViewModel by viewModels<CompositionPracticeViewModel>()
    private val checkViewModel by viewModels<CompositionCheckViewModel>()
    private val compositionViewModel by viewModels<CompositionViewModel>()

    companion object {
        private const val WORK_ID = "workId"
        private const val WORK_STATE_ID = "workStateId"

        fun start(context: Context, workId: Long, workStateId: Long) {
            val intent = Intent(context, CompositionGuidanceActivity::class.java)
            intent.putExtra(WORK_ID, workId)
            intent.putExtra(WORK_STATE_ID, workStateId)
            ActivityUtil.startActivity(context, intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
//        val gradientDrawable = GradientDrawable(
//            GradientDrawable.Orientation.LEFT_RIGHT, // 渐变方向
//            intArrayOf(
//                android.graphics.Color.parseColor("#FFC5E5FD"),
//                android.graphics.Color.parseColor("#FFCCDFFF")
//            ) // 渐变色数组
//        )
//        this.window.decorView.background = gradientDrawable
        val workId = intent.getLongExtra(WORK_ID, 0)
        val workStateId = intent.getLongExtra(WORK_STATE_ID, 0)
        // ----------------------------------------------------------------------
        compositionViewModel.aiWorkFlow.launchAndCollect(this) {
            // 接收AI工作流程的响应事件
        }
        compositionViewModel.compositionTableState.launchAndCollect(this) { status ->
            if (status is CompositionTableState.Success) {
                compositionViewModel.updateCorrectWhenCheck()
            }
        }


        // ----------------------------------------------------------------------
        setContent {
            val compositionState by compositionViewModel.compositionContentState.collectAsStateWithLifecycle()
            val selectedStep by compositionViewModel.compositionStepState.collectAsStateWithLifecycle()
            val aiProcessingState by compositionViewModel.aiProcessingState.collectAsStateWithLifecycle()
            val aiSecondResponseState by compositionViewModel.aiSecondResponseState.collectAsStateWithLifecycle()
            val isShowKeyBoard by compositionViewModel.isShowKeyBoard.collectAsStateWithLifecycle()
            LaunchedEffect("${compositionState.workId}_${compositionState.workStateId}") {
                compositionViewModel.loadStep()
            }
            CompositionScreen(selectedStep = selectedStep, onTabClicked = {
                if (aiSecondResponseState !is AiSecondResponseState.SecondOcrLoading) {
                    compositionViewModel.switchStep(it)
                }
            }, onBackPressed = {
                if (aiSecondResponseState !is AiSecondResponseState.SecondOcrLoading) {
                    finish()
                }
            }, isShowKeyBoard = isShowKeyBoard, aiProcessingState = aiProcessingState)
        }
        practiceViewModel.init()
        // 初始化作文批改ViewModel，传入作业ID和作业状态ID
        compositionViewModel.init(workId = workId, workStateId = workStateId)
        // 加载作文作业详情
        compositionViewModel.loadCompositionWork()
        // 请求AI流式处理状态（用于作文批改）
        compositionViewModel.requestAiStreamStatus()
        // 开始计时（可能是统计用户完成作文的时间）
        compositionViewModel.startCounting()
    }

//    override fun getStatusBarColorInt(): Int {
//        return android.graphics.Color.TRANSPARENT
//    }
//
//    override fun isFullScreen(): Boolean {
//        return false
//    }
//
//    override fun showNavigationBars(): Boolean {
//        return false
//    }

    override fun onRestart() {
        super.onRestart()
        compositionViewModel.resetCounting()
    }

    override fun onStop() {
        super.onStop()
        compositionViewModel.saveAndRestCounting()
        compositionViewModel.reportSwitchStepEvent()
        compositionViewModel.reportAiTabSwitchEvent()
    }

    override fun onDestroy() {
        super.onDestroy()
        LottieCompositionFactory.clearCache(this)
    }
}


