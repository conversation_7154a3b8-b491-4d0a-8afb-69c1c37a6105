package com.hailiang.composition.ui.widget

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.hailiang.composition.data.enums.JobStatus
import com.hailiang.composition.ui.practice.AiProcessingState
import com.hailiang.composition.ui.practice.AiResponseState
import com.hailiang.composition.ui.practice.AiSector
import com.hailiang.composition.ui.practice.CompositionAiProcess
import com.hailiang.composition.ui.practice.CompositionStep
import com.hailiang.composition.ui.practice.CompositionViewModel

/**
 * AI点拨 - AI结果
 */
@Composable
fun AiGuideLayout(
    compositionViewModel: CompositionViewModel,
    retake: () -> Unit,
) {
    val aiResponseState by compositionViewModel.aiResponseState.collectAsStateWithLifecycle()
    val studentFeedbackState by compositionViewModel.studentFeedbackState.collectAsStateWithLifecycle()
    val aiProcessingState by compositionViewModel.aiProcessingState.collectAsStateWithLifecycle()
    val selectedStep by compositionViewModel.compositionStepState.collectAsStateWithLifecycle()
    Column {
        if (selectedStep.showAiStatus) {
            when (aiProcessingState) {
                is AiProcessingState.GrammarChecking, is AiProcessingState.FeedbackGenerating, is
                AiProcessingState.EssayGrading -> {
                    CompositionAiProcess(
                        aiProcessingState = aiProcessingState,
                        modifier = Modifier.padding(start = 20.dp, end = 20.dp, top = 20.dp)
                    )
                }

                else -> {
//                    CompositionAiProcess(
//                        aiProcessingState = aiProcessingState,
//                        modifier = Modifier.padding(start = 20.dp, end = 20.dp, top = 20.dp)
//                    )
                }
            }
        }
        AiResultLayout(
            aiResponseState = aiResponseState,
            aiTabList = listOf(
//            AiSector.Evaluate,
                AiSector.Dibble,
                AiSector.Petrol,
            ),
            switchAiTab = {
                compositionViewModel.switchAiTab(
                    compositionStep = CompositionStep.AiGuidance, aiSector = it
                )
            },
            retry = compositionViewModel::retryAiStream,
            reload = compositionViewModel::reloadAiStream,
            retake = retake,
            bottomBar = {
//            CompositionPraiseWidget(
//                aiResponseState = aiResponseState,
//                initFeedback = studentFeedbackState,
//                feedbackClick = { aiSector, evaluateFeedback, selected ->
//                    compositionViewModel.firstDraftFeedback(
//                        aiSector, evaluateFeedback, selected
//                    )
//                },
//            )
            },
            aiProcessingState = aiProcessingState,
            initFeedback = studentFeedbackState,
            feedbackClick = { aiSector, evaluateFeedback, selected ->
                compositionViewModel.firstDraftFeedback(
                    aiSector, evaluateFeedback, selected
                )
            },
        )
    }

}

// ----------------------------------------------------------------------
// ----------------------------------------------------------------------
@Composable
@Preview(
    widthDp = 900,
    heightDp = 2000,
)
private fun AiCheckLayoutPreview() {
    AiResultLayout(
        aiTabList = listOf(
            AiSector.Evaluate,
            AiSector.Dibble,
            AiSector.Petrol,
        ), aiResponseState = AiResponseState.Success(
            allJobStatus = JobStatus.SUCCESS,
            scoreJobStatus = JobStatus.SUCCESS,
            allusionJobStatus = JobStatus.SUCCESS,
            selectedAiSector = AiSector.Dibble,
            aiSubSectorList = CompositionMockData.mockAiSubSectorList(),
            comprehensiveJudge = null,
            adviceList = null,
            allusionList = null,
            score = 18f,
            totalScore = 60
        ), switchAiTab = {}, retry = {}, reload = {

        }, bottomBar = {
            CompositionPraiseWidget(
                selectedAiSector = AiSector.Dibble,
                initFeedback = null,
                feedbackClick = null,
            )
        })
}