package com.hailiang.composition.ui.main

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.graphics.toColorInt
import com.airbnb.lottie.LottieAnimationView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.hailiang.component.FixedDrawableSizeTextView
import com.hailiang.composition.data.bean.WorkInfo
import com.hailiang.composition.data.enums.JobStatus
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.SpannableBuilder
import com.hailiang.hlutil.dp
import com.hailiang.hlutil.dpInt
import com.hailiang.hlutil.ext.clipOutline
import com.hailiang.hlutil.ext.inflate
import com.hailiang.hlutil.getDrawable
import com.hailiang.hlutil.parseJson
import com.hailiang.recyclerview.adapter.ViewHolder
import com.hailiang.recyclerview.adapter.ViewPresenter
import com.hailiang.xxb.en.composition.R

class CompositionViewPresenter : ViewPresenter<WorkInfo>() {
    override fun onCreateViewHolder(parent: ViewGroup): ViewHolder {
        return ViewHolder(parent.inflate(R.layout.item_composition).apply {
            clipOutline(15.dp)
        })
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(
        holder: ViewHolder,
        data: WorkInfo,
        position: Int
    ) {
        val llTitle = holder.getView<View>(R.id.ll_title)
        val lottieView = holder.getView<LottieAnimationView>(R.id.lottie)
        val tvTitle = holder.getView<TextView>(R.id.tv_title)
        val tvScore = holder.getView<TextView>(R.id.tv_score)
        val tvTime = holder.getView<TextView>(R.id.tv_time)
        val tvAction = holder.getView<FixedDrawableSizeTextView>(R.id.tv_action)
        val ivImg = holder.getView<ImageView>(R.id.iv_img)
        val llImagesContainer = holder.getView<LinearLayout>(R.id.ll_images_container)

        // 增强初始化，防止重用残留
        ivImg.visibility = View.GONE
        llImagesContainer.visibility = View.GONE
        llImagesContainer.removeAllViews()
        ivImg.setImageDrawable(null)
        Glide.with(ivImg.context).clear(ivImg)

        val context = holder.itemView.context

//        HLog.i(
//            "CompositionViewPresenter",
//            "Binding data: $data, imgInfoList: ${data.imgInfoList?.size}"
//        )

        when (data.printOcrStatus) {
            JobStatus.FAILED.value -> {
                lottieView.visibility = View.VISIBLE
                lottieView.setPadding(0.dpInt, 0.dpInt, 2.dpInt, 0.dpInt)
                lottieView.setImageDrawable(getDrawable(R.drawable.alert))
                tvTitle.setTextColor("#FD0002".toColorInt())
                tvTitle.text = "题目未正确识别"
            }

            JobStatus.RUNNING.value -> {
                lottieView.visibility = View.VISIBLE
                lottieView.setPadding(0, 0, 0, 0)
                lottieView.setAnimation(R.raw.lottie_ai_identifying)
                lottieView.playAnimation()
                tvTitle.setTextColor("#1759EE".toColorInt())
                tvTitle.text = "识别中…"
            }

            else -> {
                lottieView.visibility = View.GONE
                tvTitle.setTextColor("#0A0A0A".toColorInt())
                var compositionParagraphs: List<String>? = null
                if (!data.studentSecondAnswerDetail.isNullOrEmpty()) {
                    compositionParagraphs = parseJson<List<String>>(data.studentSecondAnswerDetail)
                } else if (!data.studentFirstAnswerDetail.isNullOrEmpty()) {
                    compositionParagraphs = parseJson<List<String>>(data.studentFirstAnswerDetail)
                }
                tvTitle.text = data.remark
            }
        }
        llTitle.requestLayout()

        val teacherScore = data.teacherFirstCheckScore?.toFloat() ?: 0F
        val studentScore = data.studentCheckScore?.toFloat() ?: 0F
        val sb = SpannableBuilder.newInstance("一稿：")
        if (data.commentFirstStatus == JobStatus.FAILED.value) {
            sb.append("批改出错").foregroundColor("#EB5848".toColorInt())
        } else if (studentScore > 0) {
            sb.append(studentScore.toString().removeSuffix(".0"))
                .foregroundColor("#EB5848".toColorInt())
        } else if (data.printOcrStatus == JobStatus.FAILED.value || data.ocrStatus == JobStatus.FAILED.value) {
            sb.append("识别失败").foregroundColor("#EB5848".toColorInt())
        } else {
            sb.append("生成中").foregroundColor("#598BFC".toColorInt())
        }
        sb.append("  二稿：")
        if (data.commentSecondStatus == JobStatus.FAILED.value) {
            sb.append("批改出错").foregroundColor("#EB5848".toColorInt())
        } else if (teacherScore > 0) {
            sb.append(teacherScore.toString().removeSuffix(".0"))
                .foregroundColor("#EB5848".toColorInt())
        } else if (data.status <= 2) {
            sb.append("待提交").foregroundColor("#E59F00".toColorInt())
        } else {
            sb.append("生成中").foregroundColor("#598BFC".toColorInt())
        }
        tvScore.text = sb.build()
        tvTime.text = if (data.createTime.contains(" ")) {
            data.createTime.split(" ")[0].replace("-", "/")
        } else {
            data.createTime
        }
        if (data.status == 2 && data.commentFirstStatus == JobStatus.SUCCESS.value && studentScore > 0) {
//            tvAction.text = getString(R.string.edit_composition)
            tvAction.setDrawableLeft(getDrawable(R.drawable.edit_composition))
            tvAction.visibility = View.VISIBLE
        } else {
//            tvAction.text = getString(R.string.check_report)
            tvAction.setDrawableLeft(getDrawable(R.drawable.check_report))
            tvAction.visibility = View.VISIBLE
        }

        data.imgInfoList?.let { imgList ->
            if (imgList.isNotEmpty()) {
                llImagesContainer.visibility = View.VISIBLE

                // 循环加载所有图片
                imgList.forEachIndexed { index, imgInfo ->
                    if (!imgInfo.answerPicUrl.isNullOrBlank()) {
                        val imageView = ImageView(context).apply {
                            layoutParams = LinearLayout.LayoutParams(
                                ViewGroup.LayoutParams.MATCH_PARENT,
                                ViewGroup.LayoutParams.WRAP_CONTENT
                            )
                            scaleType = ImageView.ScaleType.CENTER_CROP
                            contentDescription = "作文图片 $index"
                        }
                        llImagesContainer.addView(imageView)

                        Glide.with(context)
                            .asBitmap()
                            .load(imgInfo.answerPicUrl)
                            .diskCacheStrategy(DiskCacheStrategy.ALL)
                            .into(object : SimpleTarget<Bitmap>() {
                                override fun onResourceReady(
                                    resource: Bitmap,
                                    transition: Transition<in Bitmap>?
                                ) {
                                    val width = llImagesContainer.width
                                    val height = if (width > 0) {
                                        (width * 1f / resource.width * resource.height).toInt()
                                    } else {
                                        200.dpInt
                                    }
                                    imageView.layoutParams = LinearLayout.LayoutParams(
                                        ViewGroup.LayoutParams.MATCH_PARENT,
                                        height
                                    )
                                    imageView.setImageBitmap(resource)
//                                    HLog.i(
//                                        "CompositionViewPresenter",
//                                        "Loaded image $index, height: $height"
//                                    )
                                }

                                override fun onLoadFailed(errorDrawable: Drawable?) {
                                    HLog.w(
                                        "CompositionViewPresenter",
                                        "加载图片失败: ${imgInfo.answerPicUrl}"
                                    )
                                    imageView.setImageDrawable(errorDrawable)
                                }
                            })
                    }
                }
            } else if (imgList.size == 1) {
                // 单图情况，使用 iv_img
                val imgInfo = imgList[0]
                if (!imgInfo.answerPicUrl.isNullOrBlank()) {
                    ivImg.visibility = View.VISIBLE
//                    HLog.i("CompositionViewPresenter", "Loading URL: ${imgInfo.answerPicUrl}")
                    Glide.with(context)
                        .load(imgInfo.answerPicUrl)
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .into(ivImg)
                } else {
                    HLog.w("CompositionViewPresenter", "Invalid URL: ${imgInfo.answerPicUrl}")
                    TextView(context).apply {
                        text = "图片加载失败"
                        setTextColor("#999999".toColorInt())
                        textSize = 14f
                        layoutParams = LinearLayout.LayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT,
                            ViewGroup.LayoutParams.WRAP_CONTENT
                        ).apply {
                            topMargin = 4.dpInt
                        }
                        llImagesContainer.visibility = View.VISIBLE
                        llImagesContainer.addView(this)
                    }
                }
            }
        } ?: run {
            HLog.w("CompositionViewPresenter", "imgInfoList is null")
            TextView(context).apply {
                text = "暂无图片"
                setTextColor("#999999".toColorInt())
                textSize = 14f
                layoutParams = LinearLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                ).apply {
                    topMargin = 4.dpInt
                }
                llImagesContainer.visibility = View.VISIBLE
                llImagesContainer.addView(this)
            }
        }
    }

    override fun onUnbindViewHolder(holder: ViewHolder) {
        super.onUnbindViewHolder(holder)
        val lottieView = holder.getView<LottieAnimationView>(R.id.lottie)
        lottieView.cancelAnimation()
        lottieView.clearAnimation()
        lottieView.setImageDrawable(null)
        val ivImg = holder.getView<ImageView>(R.id.iv_img)
        val llImagesContainer = holder.getView<LinearLayout>(R.id.ll_images_container)
        Glide.with(ivImg.context).clear(ivImg)
        llImagesContainer.removeAllViews()
    }
}