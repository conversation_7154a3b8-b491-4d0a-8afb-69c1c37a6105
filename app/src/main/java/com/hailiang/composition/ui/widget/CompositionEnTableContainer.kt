package com.hailiang.composition.ui.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.res.ResourcesCompat
import com.hailiang.composition.ui.practice.AiResponseState
import com.hailiang.hlutil.DisplayUtil
import com.hailiang.hlutil.HLog
import com.hailiang.question.en.composition.EnComposition
import com.hailiang.question.en.composition.view.CompositionPaperView
import com.hailiang.ui.designsystem.util.TypeFaceHelper
import com.hailiang.xxb.en.composition.R
import com.hailiang.xxb.en.composition.databinding.LayoutCompositionEnTableBinding
import com.robertlevonyan.demo.camerax.utils.bottomMargin
import kotlinx.coroutines.delay

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/3/31 10:16
 */
open class CompositionEnTableContainer : FrameLayout {
    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    )

    constructor(
        context: Context,
        attrs: AttributeSet?,
        defStyleAttr: Int,
        defStyleRes: Int,
    ) : super(context, attrs, defStyleAttr, defStyleRes)

    val binding = LayoutCompositionEnTableBinding.inflate(LayoutInflater.from(context), this, true)
    private var preContent: String? = null
    private var preComposition: EnComposition? = null

    fun setComposition(content: String?) {
        if (preContent != content) {
            preContent = content
            binding.compositionPaperView.setContent(content ?: "")
        }
    }

    fun setComposition(composition: EnComposition?) {
        if (preComposition != composition) {
            preComposition = composition
            binding.compositionPaperView.setEnComposition(composition)
        }
    }

    fun setCompositionWatcher(watcher: CompositionPaperView.TextWatcher?) {
        watcher?.let {
            HLog.i("setCompositionWatcher", "已经设置")
            binding.compositionPaperView.setTextWatcher(it)
        }
    }

    fun setEditable(editable: Boolean) {
        binding.compositionPaperView.setPreviewMode(!editable)
    }

    fun setCompositionPaperViewAlpha(alpha: Float = 0.8f) {
        binding.compositionPaperView.alpha = alpha
    }

    fun setScore(score: Int) {
        if (score >= 0) {
            ResourcesCompat.getFont(context, R.font.table)?.let {
                TypeFaceHelper.setTypeface(binding.compositionScoreTv, it)
            }
            binding.compositionScoreTv.text = "$score"
            binding.compositionScoreTv.visibility = VISIBLE
        } else {
            binding.compositionScoreTv.visibility = GONE
        }
    }

    fun showBottomPadding(showBottomPadding: Boolean) {
        if (showBottomPadding) {
            binding.compositionPaperView.bottomMargin = DisplayUtil.pxFromDp(100F).toInt()
        } else {
            binding.compositionPaperView.bottomMargin = 0
        }
    }
}

@Composable
fun CompositionEnTableWidget(
    composition: EnComposition?,
    editable: Boolean = false,
    score: Int = -1,
    showBottomPadding: Boolean = false,
    compositionWatcher: CompositionPaperView.TextWatcher? = null,
    aiResponseState: AiResponseState? = null,
    lowerAlpha: Float = 1f,
) {
    var showToastTrigger by remember { mutableStateOf(false) }
    LaunchedEffect(aiResponseState) {
        if (aiResponseState is AiResponseState.AiFirstCorrect ||
            aiResponseState is AiResponseState.AiSecondCorrect && !showToastTrigger
        ) {
            showToastTrigger = true
        }
    }
    Box(
        modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.TopCenter
    ) {
        when {
            composition != null -> {
                AndroidView(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(16.dp))
                        .background(Color.White),
                    factory = { context ->
                        CompositionEnTableContainer(context).apply {
                            if (binding.root.parent == null) {
                                LayoutInflater.from(context)
                                    .inflate(R.layout.layout_composition_en_table, this, true)
                            }
                        }.also {
                            it.setCompositionPaperViewAlpha(lowerAlpha)
                        }
                    },
                    update = { view ->
                        view.setEditable(editable)
                        view.showBottomPadding(showBottomPadding)
                        view.setCompositionWatcher(compositionWatcher)
                        view.setComposition(composition)
                        view.setScore(score)
                    })
            }

            else -> {
                // LoadingAnimation()
            }
        }
    }
    if (showToastTrigger) {
        ToastPopup(onDismiss = { showToastTrigger = false })
    }
}

/**
 * 独立的 Toast 弹框组件 纠错完成的提示弹框
 */
@Composable
fun ToastPopup(onDismiss: () -> Unit) {
    var localShow by remember { mutableStateOf(true) }
    if (localShow) {
        Box(
            modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center,
                modifier = Modifier.clickable(onClick = {
                    HLog.i("ToastPopup", "Clicked, dismissing")
                    localShow = false
                    onDismiss()
                }, indication = null, interactionSource = remember { MutableInteractionSource() })
            ) {
                Image(
                    painter = painterResource(id = R.drawable.transparent_image_error),
                    contentDescription = null,
                    modifier = Modifier
                        .size(width = 480.dp, height = 175.dp)
                        .background(Color.Transparent)
                )
                LaunchedEffect(Unit) {
                    HLog.i("ToastPopup", "Starting 3-second delay")
                    try {
                        delay(5000L)
                        localShow = false
                        onDismiss()
                    } catch (e: Exception) {
                        HLog.e("ToastPopup", "Error in LaunchedEffect: ${e.message}")
                    }
                }
            }
        }
    }
}