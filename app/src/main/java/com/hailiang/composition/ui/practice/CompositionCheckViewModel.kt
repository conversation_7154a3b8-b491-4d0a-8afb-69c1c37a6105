package com.hailiang.composition.ui.practice

import android.app.Application
import androidx.lifecycle.viewModelScope
import com.hailiang.composition.data.Repository
import com.hailiang.composition.data.bean.CompositionCheckBean.Advice
import com.hailiang.composition.data.bean.CompositionCheckBean.Allusion
import com.hailiang.composition.data.bean.CompositionCheckBean.ComprehensiveJudge
import com.hailiang.composition.data.enums.JobStatus
import com.hailiang.core.base.BaseAndroidViewModel
import com.hailiang.core.ext.launchWithException
import com.hailiang.core.thread.ThreadPlugins
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.workcloud.data.vo.CompositionCheckDetail
import kotlinx.coroutines.flow.MutableStateFlow

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/2/21 11:02
 */
class CompositionCheckViewModel(application: Application) : BaseAndroidViewModel(application) {

    private val compositionRepository = Repository()

    // ----------------------------------------------------------------------
//    private var curWorkStateId = -1L
//    private var curWorkId = -1L

    /**
     * 综合评价
     */
    private var comprehensiveJudge: ComprehensiveJudge? = null

    /**
     * 点拨
     */
    private var adviceList: List<Advice>? = null

    /**
     * 典故列表（知识加油站）
     */
    private var allusionList: List<Allusion>? = null

    private var aiFirstCheckDetail: CompositionCheckDetail? = null

    private var stepStartTime = -1L
    private var aiStepStartTimeMillis = -1L
    // ----------------------------------------------------------------------

    val aiFirstCheckDetailState = MutableStateFlow<AiResponseState>(AiResponseState.Default)

    //
    fun loadFirstCheckDetail(workId: Long, workStateId: Long) {
        viewModelScope.launchWithException(ThreadPlugins.ioDispatcher()) {
            HLog.i(HTag.TAG, "加载AI首次批改信息: ${workId}_${workStateId}")
            aiFirstCheckDetail = compositionRepository.queryAiFirstCheckDetail(
                workId = workId,
                workStateId = workStateId,
            )
            if (aiFirstCheckDetail?.isDirty() != false) { // 请求远程数据
                compositionRepository.requestAiCheckInfo(
                    workId = workId,
                    workStateId = workStateId,
                    isSubmitted = false
                )
                aiFirstCheckDetail = compositionRepository.queryAiFirstCheckDetail(
                    workId = workId,
                    workStateId = workStateId,
                )
            }
            updateAiFirstCheckState(
                AiResponseState.Success(
                    allJobStatus = JobStatus.SUCCESS,
                    scoreJobStatus = JobStatus.SUCCESS,
                    allusionJobStatus = JobStatus.SUCCESS,
                    selectedAiSector = AiSector.Dibble,
                    aiSubSectorList = AiSector.Dibble.getAiSubSectorList(
                        comprehensiveJudge = aiFirstCheckDetail?.comprehensiveJudge,
                        adviceList = aiFirstCheckDetail?.adviceList,
                        allusionList = aiFirstCheckDetail?.allusionList
                    ),
                    comprehensiveJudge = aiFirstCheckDetail?.comprehensiveJudge,
                    adviceList = aiFirstCheckDetail?.adviceList,
                    allusionList = aiFirstCheckDetail?.allusionList,
                    score = aiFirstCheckDetail?.score ?: 0f,
                    totalScore = aiFirstCheckDetail?.totalScore ?: 0,
                )
            )
        }
    }

    fun switchFirstAiTab(compositionStep: CompositionStep, aiSector: AiSector) {
        viewModelScope.launchWithException {
            val aiCorrectState = aiFirstCheckDetailState.value
            if (aiCorrectState is AiResponseState.Success) {
                updateAiFirstCheckState(
                    aiCorrectState.copy(
                        selectedAiSector = aiSector,
                        aiSubSectorList = aiSector.getAiSubSectorList(
                            comprehensiveJudge = aiFirstCheckDetail?.comprehensiveJudge,
                            adviceList = aiFirstCheckDetail?.adviceList,
                            allusionList = aiFirstCheckDetail?.allusionList
                        )
                    )
                )
            }
        }
    }

    private fun updateAiFirstCheckState(aiResponseState: AiResponseState) {
        aiFirstCheckDetailState.value = aiResponseState
    }


    // ----------------------------------------------------------------------
    override fun onCleared() {
        super.onCleared()
    }

    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
}