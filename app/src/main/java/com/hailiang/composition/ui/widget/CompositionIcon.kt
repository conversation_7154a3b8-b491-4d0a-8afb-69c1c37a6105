package com.hailiang.composition.ui.widget

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.hailiang.xxb.en.composition.R

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/3/13 22:03
 */
@Composable
internal fun CompositionSelectedIcon(
    modifier: Modifier = Modifier,
    @DrawableRes normalRes: Int,
    @DrawableRes selectedRes: Int,
    isSelected: Boolean,
    onClick: (() -> Unit)? = null,
) {
    Image(
        modifier = modifier
            .clickable(
                enabled = onClick != null,
                indication = null,
                interactionSource = remember { MutableInteractionSource() },
            ) {
                onClick?.invoke()
            },
        painter = painterResource(if (isSelected) selectedRes else normalRes),
        contentDescription = null
    )
}

@Composable
fun CircleIndicatorIcon(
    modifier: Modifier = Modifier,
    index: Int,
    indicatorColor: Color,
    fontSize: TextUnit = 16.sp,
    fontColor: Color = Color.White,
) {
    Box(
        modifier = modifier
            .width(22.dp)
            .height(22.dp)
            .background(color = indicatorColor, shape = CircleShape)
    ) {
        Text(
            modifier = Modifier.align(alignment = Alignment.Center),
            text = "$index",
            color = fontColor,
            fontSize = fontSize
        )
    }
}

@Composable
fun RestoreBackIcon(modifier: Modifier = Modifier, enable: Boolean =true, back: () -> Unit) {
    Row(
        modifier = modifier
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null,
            ) {
                if (enable) {
                    back.invoke()
                }
            }, verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            modifier = Modifier.size(24.dp)
                .alpha(if (enable) 1f else 0.4f),
            painter = painterResource(R.drawable.ic_return),
            contentDescription = null
        )
        Spacer(modifier = Modifier.width(6.dp))
        Text(
            text = "还原",
            color = Color(0x1759EE).copy(alpha = if (enable) 1f else 0.4f),
            fontWeight = FontWeight.Bold,
            fontSize = 18.sp
        )
    }
}

@Composable
@Preview(
    widthDp = 800,
)
private fun CompositionIconPreview() {
    Column(modifier = Modifier.background(Color(0xFF00F0F0))) {
        CompositionSelectedIcon(
            normalRes = R.drawable.icon_evaluate_normal,
            selectedRes = R.drawable.icon_evaluate_selected,
            isSelected = true,
            onClick = {})

        CircleIndicatorIcon(
            modifier = Modifier
                .width(20.dp)
                .height(20.dp),
            index = 1,
            indicatorColor = Color.Red,
        )
    }
}