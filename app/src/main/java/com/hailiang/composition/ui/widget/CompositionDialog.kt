package com.hailiang.composition.ui.widget

import android.app.Activity
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import com.hailiang.xxb.en.composition.R


class SubmitSuccessWindow(val context: Context) {
    private val handler = Handler(Looper.getMainLooper())
    private var dismissRunnable: Runnable? = null
    private val autoCloseTime = 3000L
    private var maskView: View? = null // 新增：遮罩层引用

    fun show() {
        val view = (context as Activity).window.decorView as ViewGroup
        dismiss()
        dismissRunnable = Runnable { dismiss() }

        // 添加遮罩层
        maskView = View(context).apply {
            setBackgroundColor(0x80000000.toInt()) // 半透明黑色
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            // 拦截并消耗点击事件
            setOnClickListener { /* 空实现，仅消耗事件 */ }
            isClickable = true // 确保视图可点击
        }
        view.addView(maskView)

        val popupView = ComposeView(context).apply {
            id = R.id.success_pop_window
            setContent {
                Popup(
                    alignment = Alignment.Center, onDismissRequest = {
                        dismiss()
                    }) {
                    SubmitSuccessDialog {
                        dismiss()
                    }
                }
            }
        }
        view.addView(popupView)
        dismissRunnable?.let {
            handler.postDelayed(it, autoCloseTime)
        }

    }

    fun dismiss() {
        dismissRunnable?.let {
            handler.removeCallbacks(it)
            dismissRunnable = null
        }
        val view = (context as Activity).window.decorView as ViewGroup
        view.findViewById<View>(R.id.success_pop_window)?.let {
            view.removeView(it)
        }
        // 移除遮罩层
        maskView?.let {
            view.removeView(it)
            maskView = null
        }
    }
}


@Composable
fun SubmitSuccessDialog(
    onDismissRequest: () -> Unit
) {
    val interactionSource = remember { MutableInteractionSource() } // 用于 clickable
    Column(
        modifier = Modifier
            .height(220.dp)
            .width(400.dp)
            .shadow(
                elevation = 8.dp, shape = RoundedCornerShape(CORNER_RADIUS)
            )
            .background(
                color = Color.White, shape = RoundedCornerShape(CORNER_RADIUS)
            )
            .clickable( // 添加点击事件来关闭对话框
                interactionSource = interactionSource, // 提供交互源
                indication = null, // null 表示不显示默认的点击涟漪效果
                onClick = {
                    onDismissRequest()
                }),
        horizontalAlignment = Alignment.CenterHorizontally, verticalArrangement = Arrangement.Center
    ) {
        Text(
            modifier = Modifier.padding(top = 24.dp),
            text = "二稿提交成功",
            fontSize = 22.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black
        )
        Image(
            painter = painterResource(R.drawable.icon_submit_success),
            contentDescription = null, // 点击整个对话框关闭，此图片无需单独描述
            modifier = Modifier
                .width(150.dp)
                .height(130.dp)
                .padding(bottom = 24.dp)
        )
    }
}


@Preview(showBackground = true)
@Composable
fun SubmitSuccessDialogPreview() {
    SubmitSuccessDialog() {}
}
