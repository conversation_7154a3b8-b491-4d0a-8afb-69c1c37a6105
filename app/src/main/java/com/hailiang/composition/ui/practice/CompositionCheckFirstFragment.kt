package com.hailiang.composition.ui.practice

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.getValue
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.hailiang.common.base.BaseFragment
import com.hailiang.composition.ui.widget.CompositionCheckFirstLayout
import com.hailiang.composition.ui.widget.CompositionEnTableWidget
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.dp
import com.hailiang.hlutil.ext.clipOutline
import com.hailiang.xxb.en.composition.R
import com.hailiang.xxb.en.composition.databinding.FragmentCompositionCheckFirstBinding


/**
 * Description: 查看首次作答
 *
 * <AUTHOR>
 * @version 2025/2/21 10:58
 */
class CompositionCheckFirstFragment : BaseFragment(R.layout.fragment_composition_check_first) {
    //    private val imageLayout: FrameLayout by id(R.id.composition_student_images_container)
//    private val contentLayout: FrameLayout by id(R.id.composition_ai_content_container)
    private val compositionViewModel by activityViewModels<CompositionViewModel>()
    private val compositionCheckViewModel by activityViewModels<CompositionCheckViewModel>()
    private var _binding: FragmentCompositionCheckFirstBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = FragmentCompositionCheckFirstBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.compositionAiContentContainer.clipOutline(15.dp)
        binding.compositionStudentImagesContainer.clipOutline(15.dp)
        // 一稿纠错信息
        binding.compositionStudentImagesContainer.addView(
            ComposeView(requireActivity()).apply {
                setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
                setContent {
                    val compositionCorrectTableState by compositionViewModel.compositionCorrectTableState.collectAsStateWithLifecycle()
                    CompositionEnTableWidget(composition = compositionCorrectTableState)
                }
            }
        )
        binding.compositionAiContentContainer.addView(
            ComposeView(requireActivity()).apply {
                setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
                setContent {
                    val aiCorrectState by compositionCheckViewModel.aiFirstCheckDetailState.collectAsStateWithLifecycle()
                    val studentFeedbackState by compositionViewModel.studentFeedbackState.collectAsStateWithLifecycle()
                    val aiProcessingState by compositionViewModel.aiProcessingState.collectAsStateWithLifecycle()

                    CompositionCheckFirstLayout(
                        aiResponseState = aiCorrectState,
                        studentFeedbackState = studentFeedbackState,
                        switchAiTab = {
                            compositionViewModel.reportAiTabSwitchEvent(
                                CompositionStep.CheckFirstPractice,
                                aiSector = it
                            )
                            compositionCheckViewModel.switchFirstAiTab(
                                compositionStep = CompositionStep.CheckFirstPractice,
                                aiSector = it
                            )
                        },
                        retry = {},
                        reload = {},
                        feedbackClick = { aiSector, evaluateFeedback, selected ->
                            compositionViewModel.firstDraftFeedback(
                                aiSector,
                                evaluateFeedback,
                                selected
                            )
                        },
                        aiProcessingState = aiProcessingState,
                    )
                }
            }
        )
        compositionCheckViewModel.loadFirstCheckDetail(
            workId = compositionViewModel.curWorkId,
            workStateId = compositionViewModel.curWorkStateId
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}