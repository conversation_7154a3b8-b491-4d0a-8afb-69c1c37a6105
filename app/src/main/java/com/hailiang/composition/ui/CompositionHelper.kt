package com.hailiang.composition.ui

import com.hailiang.composition.data.bean.CompositionCheckBean.CheckContent
import com.hailiang.composition.data.bean.CompositionCheckBean.CorrectWord
import com.hailiang.composition.data.bean.WorkDetail
import com.hailiang.composition.data.enums.CorrectWordType
import com.hailiang.composition.data.enums.CorrectWordWay
import com.hailiang.hlutil.ext.jsonToList
import com.hailiang.question.en.composition.Correct
import com.hailiang.question.en.composition.EnComposition
import com.hailiang.question.en.composition.ErrorType
import com.hailiang.workcloud.data.vo.CompositionCheckDetail
import com.hailiang.workcloud.data.vo.CompositionPractice

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/3/16 02:53
 */
object CompositionHelper {

    fun obtainEnCorrectCompositionTableBean(
        compositionPractice: CompositionPractice?,
        compositionCheckDetail: CompositionCheckDetail?,
    ): EnComposition? {
        return obtainEnCorrectCompositionTableBean(
            ocrContentList = compositionCheckDetail?.ocrContent?.jsonToList(String::class.java),
            correctWords = compositionCheckDetail?.correctWords
        )
    }

    fun obtainSecondEnCorrectCompositionTableBean(
        compositionPractice: CompositionPractice?,
        compositionCheckDetail: CompositionCheckDetail?,
    ): EnComposition? {
        return obtainEnCorrectCompositionTableBean(
            ocrContentList = compositionPractice?.secondPracticeContent?.jsonToList(String::class.java),
            correctWords = compositionCheckDetail?.correctWords
        )
    }


    fun obtainEnCorrectCompositionTableBean(checkContent: CheckContent?): EnComposition? {
        return obtainEnCorrectCompositionTableBean(
            ocrContentList = checkContent?.ocrResult?.content, correctWords = checkContent?.correctResult?.correctWords
        )
    }

    fun obtainEnCorrectCompositionTableBean(
        compositionPractice: CompositionPractice?, checkContent: WorkDetail?
    ): EnComposition? {
        val studentFirstAnswerDetail = checkContent?.studentAnswerInfo?.studentFirstAnswerDetail
        val studentSecondAnswerDetail =
            compositionPractice?.secondPracticeContent ?: checkContent?.studentAnswerInfo?.studentSecondAnswerDetail
        val content = studentSecondAnswerDetail?.jsonToList(String::class.java) ?: studentFirstAnswerDetail?.jsonToList(
            String::class.java
        )
        return obtainEnCorrectCompositionTableBean(content, null)
    }


    fun obtainEnCorrectCompositionTableBean(checkContent: WorkDetail?): EnComposition? {
        val studentFirstAnswerDetail = checkContent?.studentAnswerInfo?.studentFirstAnswerDetail
        val studentSecondAnswerDetail = checkContent?.studentAnswerInfo?.studentSecondAnswerDetail
        val content = studentSecondAnswerDetail?.jsonToList(String::class.java) ?: studentFirstAnswerDetail?.jsonToList(
            String::class.java
        )
        return obtainEnCorrectCompositionTableBean(content, null)
    }

    fun obtainEnCorrectCompositionTableBean(
        ocrContentList: List<String>?,
        correctWords: List<CorrectWord>?,
    ): EnComposition? {
        val content = formatContent(ocrContentList)
        if (content == null) {
            return null
        }
        val composition = EnComposition(
            content = content, correctList = obtainCorrectInfos(correctWords)
        )
        return composition
    }

    private fun formatContent(content: List<String>?): String? {
        return content?.joinToString("\n")
    }

    private fun obtainCorrectInfos(
        correctWords: List<CorrectWord>?,
    ): List<Correct>? {
        return correctWords?.mapNotNull {
            when (it.getCorrectWordWay()) {
                CorrectWordWay.ADD -> {
                    Correct.Insert(
                        signContent = it.originContent,
                        index = it.index,
                        before = it.before,
                        after = it.after,
                        errorType = it.asErrorType(),
                        reason = it.reason
                    )
                }

                CorrectWordWay.DELETE -> {
                    Correct.Delete(
                        signContent = it.originContent,
                        index = it.index,
                        before = it.before,
                        after = it.after,
                        errorType = it.asErrorType(),
                        reason = it.reason
                    )
                }

                CorrectWordWay.UPDATE -> {
                    Correct.Replace(
                        signContent = it.originContent,
                        index = it.index,
                        before = it.before,
                        after = it.after,
                        errorType = it.asErrorType(),
                        reason = it.reason
                    )
                }

                else -> {
                    null
                }
            }
        }
    }
}

fun CorrectWord.asErrorType(): ErrorType {
    return when (getCorrectWorType()) {
        CorrectWordType.MINOR -> {
            ErrorType.MINOR
        }

        CorrectWordType.SERIOUS -> {
            ErrorType.SERIOUS
        }

        else -> {
            ErrorType.MINOR
        }
    }
}


