<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
		xmlns:app="http://schemas.android.com/apk/res-auto"
		xmlns:tools="http://schemas.android.com/tools"
		android:layout_width="match_parent"
		android:layout_height="match_parent">

	<FrameLayout
			android:id="@+id/composition_student_images_container"
			android:layout_width="@dimen/composition_student_image_container_width"
			android:layout_height="0dp"
			android:layout_marginStart="@dimen/dp_24"
			android:layout_marginTop="@dimen/dp_6"
			android:layout_marginBottom="@dimen/dp_24"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintTop_toTopOf="parent" />

	<FrameLayout
			android:id="@+id/composition_ai_content_container"
			android:layout_width="@dimen/composition_student_image_container_width"
			android:layout_height="0dp"
			android:layout_marginTop="@dimen/dp_6"
			android:layout_marginEnd="@dimen/dp_24"
			android:layout_marginBottom="@dimen/dp_24"
			android:background="@drawable/gradient_rounded_background"
			android:contentDescription="@null"
			android:scrollbars="none"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent" />


	<ImageView
			android:id="@+id/composition_second_practice_btn"
			android:layout_width="@dimen/dp_86"
			android:layout_height="@dimen/dp_86"
			android:layout_marginEnd="@dimen/dp_10"
			android:layout_marginBottom="@dimen/dp_10"
			android:visibility="gone"
			tools:visibility="visible"
			android:contentDescription="@null"
			android:src="@drawable/btn_second_practice"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>