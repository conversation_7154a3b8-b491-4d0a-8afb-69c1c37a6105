<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
		xmlns:app="http://schemas.android.com/apk/res-auto"
		xmlns:tools="http://schemas.android.com/tools"
		android:layout_width="match_parent"
		android:layout_height="match_parent"
		android:layout_marginHorizontal="24dp"
		android:layout_marginTop="20dp"
		android:layout_marginBottom="20dp"
		android:paddingVertical="16dp">

	<com.hailiang.component.shape.GradientTextView
			android:layout_width="match_parent"
			android:layout_height="match_parent"
			app:cornerRadius="15dp"
			app:solidColor="@color/white" />

	<ScrollView
			android:layout_width="match_parent"
			android:layout_height="match_parent"
			android:layout_marginTop="20dp"
			android:layout_marginBottom="16dp"
			android:layout_marginHorizontal="16dp">

		<LinearLayout
				android:id="@+id/ll_question_topic"
				android:layout_width="match_parent"
				android:layout_height="wrap_content"
				android:orientation="vertical"
				app:layout_constraintTop_toTopOf="parent" />
	</ScrollView>

	<FrameLayout
			android:id="@+id/composition_ai_content_container"
			android:layout_width="match_parent"
			android:layout_height="match_parent"
			android:contentDescription="@null"
			android:scrollbars="none" />

	<View
			android:layout_width="0dp"
			android:layout_height="10dp"
			android:background="@drawable/ic_first_title_bg"
			app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
			app:layout_constraintEnd_toEndOf="@+id/tvTitle"
			android:visibility="gone"
			app:layout_constraintStart_toStartOf="@+id/tvTitle" />

	<ImageView
			android:id="@+id/iv_img"
			android:layout_width="24dp"
			android:layout_height="24dp"
			android:layout_marginStart="28dp"
			android:layout_marginTop="20dp"
			android:src="@drawable/icon_english"
			android:visibility="gone"
			app:layout_constraintStart_toStartOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			app:layout_constraintBottom_toBottomOf="@+id/tvTitle" />

	<TextView
			android:id="@+id/tvTitle"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginStart="8dp"
			android:layout_marginTop="20dp"
			android:text="我的作文"
			android:textColor="@color/black"
			android:textSize="24sp"
			android:textStyle="bold"
			android:visibility="gone"
			app:layout_constraintStart_toEndOf="@+id/iv_img"
			app:layout_constraintTop_toTopOf="parent"
			tools:ignore="HardcodedText" />


</androidx.constraintlayout.widget.ConstraintLayout>