<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
		xmlns:app="http://schemas.android.com/apk/res-auto"
		android:layout_width="match_parent"
		android:layout_height="match_parent">

	<FrameLayout
			android:id="@+id/composition_student_content_container"
			android:layout_width="@dimen/composition_student_image_container_width"
			android:layout_height="0dp"
			android:layout_marginStart="@dimen/dp_24"
			android:layout_marginTop="@dimen/dp_6"
			android:layout_marginBottom="@dimen/dp_24"
			android:background="@color/white"
			android:contentDescription="@null"
			android:paddingVertical="@dimen/dp_10"
			android:scrollbars="none"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintTop_toTopOf="parent" />


	<FrameLayout
			android:id="@+id/composition_teacher_check_container"
			android:layout_width="@dimen/composition_student_image_container_width"
			android:layout_height="0dp"
			android:layout_marginTop="@dimen/dp_6"
			android:layout_marginEnd="@dimen/dp_24"
			android:layout_marginBottom="@dimen/dp_24"
			android:background="@color/white"
			android:contentDescription="@null"
			android:scrollbars="none"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>