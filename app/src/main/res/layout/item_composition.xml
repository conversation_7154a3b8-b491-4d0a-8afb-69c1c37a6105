<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
		xmlns:app="http://schemas.android.com/apk/res-auto"
		xmlns:tools="http://schemas.android.com/tools"
		android:layout_width="389dp"
		android:layout_height="274dp"
		android:background="@color/white"
		tools:ignore="ExtraText">

	<!-- 图片区域保持在中间，但其上下约束会改变 -->
	<androidx.cardview.widget.CardView
			android:id="@+id/ll_images_card_view"
			android:layout_width="0dp"
			android:layout_height="0dp"
			android:layout_marginHorizontal="6dp"
			android:layout_marginTop="6dp"
			android:layout_marginBottom="6dp"
			app:cardCornerRadius="8dp"
			app:cardElevation="0dp"
			app:cardUseCompatPadding="true"
			app:layout_constraintTop_toTopOf="parent"
			app:layout_constraintBottom_toTopOf="@+id/fl_bottom_content_container"
			app:layout_constraintStart_toStartOf="parent"
			app:layout_constraintEnd_toEndOf="parent">

		<LinearLayout
				android:id="@+id/ll_images_container"
				android:layout_width="match_parent"
				android:layout_height="wrap_content"
				android:orientation="vertical" />

		<ImageView
				android:id="@+id/iv_img"
				android:layout_width="match_parent"
				android:layout_height="match_parent"
				android:scaleType="centerCrop"
				android:contentDescription="作文图片"
				android:visibility="gone" />
	</androidx.cardview.widget.CardView>

	<!-- 新的底部内容容器，使用 ConstraintLayout 来左右排列两组垂直元素 -->
	<androidx.constraintlayout.widget.ConstraintLayout
			android:id="@+id/fl_bottom_content_container"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:paddingHorizontal="1dp"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintStart_toStartOf="parent"
			app:layout_constraintEnd_toEndOf="parent">

		<!-- 左侧：标题和分数 (垂直排列) -->
		<LinearLayout
				android:id="@+id/ll_title_score_group"
				android:layout_width="wrap_content"
				android:layout_height="wrap_content"
				android:orientation="vertical"
				android:gravity="start|center_vertical"
				android:layout_marginStart="14dp"
				android:layout_marginBottom="10dp"
				android:layout_marginTop="12dp"

				app:layout_constraintStart_toStartOf="parent"
				app:layout_constraintTop_toTopOf="parent"
				app:layout_constraintBottom_toBottomOf="parent">

			<!-- 标题部分 -->
			<LinearLayout
					android:id="@+id/ll_title"
					android:layout_width="wrap_content"
					android:layout_height="wrap_content"
					android:gravity="center_vertical"
					android:orientation="horizontal">

				<com.airbnb.lottie.LottieAnimationView
						android:id="@+id/lottie"
						android:layout_width="16dp"
						android:layout_height="16dp"
						app:lottie_repeatCount="-1" />

				<TextView
						android:id="@+id/tv_title"
						android:layout_width="wrap_content"
						android:layout_height="wrap_content"
						android:maxWidth="170dp"
						android:singleLine="true"
						android:textColor="#0A0A0A"
						android:textSize="16sp"
						android:textStyle="bold"
						tools:text="作文标题" />
			</LinearLayout>

			<!-- 分数 -->
			<TextView
					android:id="@+id/tv_score"
					android:layout_width="wrap_content"
					android:layout_height="wrap_content"
					android:layout_marginTop="4dp"
					android:textColor="#515A6E"
					android:textSize="14sp"
					tools:text="48" />
		</LinearLayout>


		<!-- 右侧：时间和操作按钮 (垂直排列) -->
		<LinearLayout
				android:id="@+id/ll_time_action_group"
				android:layout_width="wrap_content"
				android:layout_height="wrap_content"
				android:orientation="vertical"
				android:gravity="end|center_vertical"
				android:layout_marginEnd="14dp"
				android:layout_marginBottom="10dp"
				android:layout_marginTop="12dp"
				app:layout_constraintEnd_toEndOf="parent"
				app:layout_constraintTop_toTopOf="parent"
				app:layout_constraintBottom_toBottomOf="parent">

			<!-- 时间 -->
			<com.hailiang.component.FixedDrawableSizeTextView
					android:id="@+id/tv_time"
					android:layout_width="wrap_content"
					android:layout_height="wrap_content"
					android:drawablePadding="6dp"
					android:textColor="#515A6E"
					android:textSize="14sp"
					app:drawableHeight="15dp"
					app:drawableWidth="15dp"
					tools:ignore="HardcodedText,RtlHardcoded"
					tools:text="2025/05/16" />

			<!-- 操作按钮和删除图标 -->
			<FrameLayout
					android:layout_width="wrap_content"
					android:layout_height="wrap_content"
					android:layout_marginTop="4dp">

				<LinearLayout
						android:layout_width="wrap_content"
						android:layout_height="wrap_content"
						android:orientation="horizontal"
						android:gravity="center_vertical">

					<com.hailiang.component.FixedDrawableSizeTextView
							android:id="@+id/tv_action"
							android:layout_width="wrap_content"
							android:layout_height="wrap_content"
							android:drawableLeft="@drawable/check_report"
							app:drawableHeight="20dp"
							app:drawableWidth="20dp"
							tools:text="" />
					<!-- 移除了 textColor, textSize, textStyle 因为主要显示图标 -->

					<ImageView
							android:id="@+id/iv_delete"
							android:layout_width="20dp"
							android:layout_height="20dp"
							android:layout_marginStart="22dp"
							android:importantForAccessibility="no"
							android:src="@drawable/delete_report"
							android:scaleType="centerInside" />
					<!-- 移除了 layout_gravity 因为它现在由父 LinearLayout 控制 -->

				</LinearLayout>

			</FrameLayout>
		</LinearLayout>

		<!-- 背景 GradientTextView (如果选择使用它作为背景) -->
		<com.hailiang.component.shape.GradientTextView
				android:id="@+id/fl_bottom_background"
				android:layout_width="0dp"
				android:layout_height="0dp"
				app:layout_constraintTop_toTopOf="parent"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintStart_toStartOf="parent"
				app:layout_constraintEnd_toEndOf="parent"
				app:bottomLeftRadius="15dp"
				app:bottomRightRadius="15dp"
				app:solidColor="#F3F6FF"
				app:topLeftRadius="2dp"
				app:topRightRadius="2dp"
				android:elevation="-1dp"
				app:layout_constraintHorizontal_bias="0.0"
				app:layout_constraintVertical_bias="0.0" />

	</androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>